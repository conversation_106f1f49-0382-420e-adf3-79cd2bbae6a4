# -*- coding: gbk -*-
# 股票对比分析程序

import os
import itertools
import time
import sys
import requests  # 添加requests库用于HTTP请求
import json  # 添加json库用于处理响应
import traceback  # 添加用于详细错误跟踪
from deepseek_client import DeepSeekClient
# 导入stock_tech模块
import Zstock_tech_qmttdx_bak as stock_tech  # 修改为Zstock_tech_qmttdx，统一使用QMT数据源
import concurrent.futures
import threading
from queue import Queue
import re
import pandas as pd
import glob
from datetime import datetime
import akshare as ak  # 导入akshare获取行业信息
import logging  # 添加日志模块，用于更好的错误处理
import Zstock_score0724 as stock_score  # 引入本地评分模块

# 导入缠论3买分析模块用于盈亏比计算
try:
    import Zstock_find3buy
    CHAN_ANALYSIS_AVAILABLE = True
except ImportError as e:
    print(f"缠论分析模块导入失败: {e}")
    CHAN_ANALYSIS_AVAILABLE = False

# 导入新的中枢指标接口
try:
    from Zstock_zs_indicators import get_zs_indicators
    try:
        from Common.CEnum import KL_TYPE
    except ImportError:
        from libs.chan.Common.CEnum import KL_TYPE
    ZS_INDICATORS_AVAILABLE = True
    print("成功导入中枢指标接口模块")
except ImportError as e:
    print(f"中枢指标接口模块导入失败: {e}")
    ZS_INDICATORS_AVAILABLE = False
    # 定义基本的KL_TYPE枚举用于兼容
    class KL_TYPE:
        K_DAY = "K_DAY"
        K_WEEK = "K_WEEK"
        K_30M = "K_30M"

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("stock_compare.log", mode='w', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 添加过滤器，屏蔽某些特定错误消息
class ErrorFilter(logging.Filter):
    def filter(self, record):
        # 过滤掉包含特定字符串的日志消息
        filtered_strings = [
            "文件不存在: D:\\new_tdx\\vipdoc\\sz\\lday\\sz",
            "未找到股票/板块sz",
            "vipdoc"
        ]
        return not any(s in record.getMessage() for s in filtered_strings)

# 为文件处理器添加过滤器
for handler in logger.handlers:
    if isinstance(handler, logging.FileHandler):
        handler.addFilter(ErrorFilter())

# 重定向print函数到logger
def custom_print(*args, **kwargs):
    message = ' '.join(map(str, args))
    if any(s in message for s in ["文件不存在", "未找到股票/板块", "vipdoc"]):
        logger.debug(message)  # 将匹配的消息降级为debug级别
    else:
        logger.info(message)

# 替换内置print函数
original_print = print
print = custom_print

# 添加技术指标获取锁，防止并发冲突
tech_lock = threading.Lock()

def get_timestamp():
    """获取格式化的时间戳，用于日志文件命名"""
    return datetime.now().strftime("%Y%m%d_%H%M%S")

def get_alternating_log_file():
    """获取轮流使用的日志文件名"""
    # 检查两个日志文件的存在情况和修改时间
    file1 = "compare_results1.txt"
    file2 = "compare_results2.txt"

    # 如果两个文件都不存在，使用文件1
    if not os.path.exists(file1) and not os.path.exists(file2):
        return file1

    # 如果只有一个文件存在，使用另一个
    if os.path.exists(file1) and not os.path.exists(file2):
        return file2
    if os.path.exists(file2) and not os.path.exists(file1):
        return file1

    # 如果两个文件都存在，使用修改时间较早的那个
    try:
        mtime1 = os.path.getmtime(file1)
        mtime2 = os.path.getmtime(file2)
        return file1 if mtime1 <= mtime2 else file2
    except:
        # 如果获取修改时间失败，默认使用文件1
        return file1

def check_and_reconnect(client):
    """检查并尝试重新连接客户端"""
    if client is None:
        return False
    try:
        # 尝试重新连接
        if hasattr(client, 'connect'):
            return client.connect()
        else:
            return True  # 如果没有connect方法，假设连接正常
    except Exception as e:
        print(f"重新连接失败: {e}")
        return False

def analyze_industry_properly(industry_name):
    """
    正确地分析行业板块，参考Zstock_score.py的实现
    
    Args:
        industry_name: 行业名称，如'电池'、'半导体'等
        
    Returns:
        str: 行业技术指标分析结果
    """
    # 导入板块映射相关工具（仍使用 Zstock_industry_tdx 提供的映射与工具函数）
    try:
        from Zstock_industry_tdx import (
            BLOCK_NAME_CODE_MAP,
            build_block_code_map,
            find_latest_block_file
        )
    except ImportError:
        print(f"无法导入Zstock_industry_tdx模块，行业 '{industry_name}' 技术指标获取失败")
        return f"{industry_name} 的技术指标分析：\n\n行业技术指标数据暂不可用，建议关注个股具体表现。"
    
    # 记录原始行业名称，用于显示
    original_industry_name = industry_name
    
    # 先使用预定义的板块映射
    block_code_map = BLOCK_NAME_CODE_MAP
    
    # 尝试查找最新的板块指数文件，特别是今日的文件
    latest_block_file = find_latest_block_file()
    current_date = datetime.now().strftime('%Y%m%d')
    today_block_file = None
    
    # 检查是否有当日的板块指数文件
    tdxdata_dir = "tdxdata"  # 默认目录
    today_file_name = f"板块指数{current_date}.xls"
    candidate_paths = [
        os.path.join("D:\\stock", tdxdata_dir, today_file_name),
        os.path.join(tdxdata_dir, today_file_name),
        os.path.join(".", tdxdata_dir, today_file_name)
    ]
    
    for path in candidate_paths:
        if os.path.exists(path):
            today_block_file = path
            print(f"找到当日板块指数文件: {path}")
            break
    
    # 优先使用当日文件，其次使用最新文件
    block_file_to_use = today_block_file or latest_block_file
    
    if block_file_to_use:
        # 如果找到文件，使用文件中的映射更新预定义映射
        print(f"使用板块指数文件: {block_file_to_use}")
        file_block_map = build_block_code_map(block_file_to_use)
        if file_block_map:
            # 合并两个映射，文件中的映射优先
            block_code_map.update(file_block_map)
    
    # 初始化input_code为None
    input_code = None
    input_code_type = None  # 用于记录代码类型
    matched_block_name = None  # 用于记录匹配到的板块名称
    
    # 检查行业名称是否直接在映射中
    if industry_name in block_code_map:
        input_code = block_code_map[industry_name]
        input_code_type = "板块"
        matched_block_name = industry_name
        print(f"板块名称 '{industry_name}' 对应的代码是: {input_code}")
    else:
        # 尝试模糊匹配板块名称
        matched_blocks = []
        for block_name, block_code in block_code_map.items():
            if industry_name in block_name or block_name in industry_name:
                matched_blocks.append((block_name, block_code))
        
        if matched_blocks:
            # 只使用第一个匹配结果
            matched_block_name = matched_blocks[0][0]
            input_code = matched_blocks[0][1]
            input_code_type = "板块"
            print(f"找到匹配的板块: {matched_block_name}，代码: {input_code}")
        
        # 如果仍然没有找到对应代码，尝试直接作为股票代码
        if not input_code and len(industry_name) == 6 and industry_name.isdigit():
            input_code = industry_name
            input_code_type = "股票"
            print(f"使用输入的代码: {input_code}")
    
    if not input_code:
        # 尝试直接生成板块代码
        # 常见的行业板块代码格式
        if len(industry_name) <= 4:  # 短名称可能是行业分类
            # 尝试常见的板块代码前缀
            for prefix in ['880', '881', '399']:
                # 创建一个候选代码
                candidate_code = f"{prefix}{industry_name[:3].zfill(3)}"
                print(f"尝试生成板块代码: {candidate_code}")
                
                # 这里应该有更复杂的验证逻辑，但简化为直接返回
                input_code = candidate_code
                input_code_type = "板块"
                matched_block_name = industry_name  # 使用原始行业名称
                break
    
    if not input_code:
        error_msg = f"未能找到行业 '{industry_name}' 对应的板块代码"
        print(error_msg)
        return f"{industry_name} 的技术指标分析：\n\n行业技术指标数据暂不可用，建议关注个股具体表现。"
    
    # 调用 Zstock_tech_qmttdx.analyze_stock 进行分析
    try:
        # 根据代码类型决定数据源：板块使用 'tdx'，个股使用 'miniqmt'
        data_source = 'tdx' if input_code_type == '板块' else 'miniqmt'

        # 对于个股也可以直接调用，同一接口保持兼容
        result = stock_tech.analyze_stock(input_code, data_source=data_source)
        
        # 如果result是字符串且包含标题行，修改标题
        if isinstance(result, str):
            # 统一将标题中的 "{code} 的技术指标分析" 替换为 "{industry_name} 的技术指标分析"
            display_name = matched_block_name or original_industry_name
            old_header = f"{input_code} 的技术指标分析："
            # 也兼容旧格式 "板块代码 {code} 的技术指标分析："
            old_header_alt = f"板块代码 {input_code} 的技术指标分析："
            new_header = f"{display_name} 的技术指标分析："
            result = result.replace(old_header_alt, new_header)
            result = result.replace(old_header, new_header)
            
            # 如果包含"使用模拟历史数据"的注释，添加解释
            if "使用模拟历史数据" in result:
                explanation = "\n注: '使用模拟历史数据' 表示系统无法找到真实历史交易数据，使用了基于当前数据的算法模拟生成的历史数据。"
                # 在结果末尾添加解释
                result += explanation
                
        return result
    except Exception as e:
        error_msg = f"分析行业 '{industry_name}' (代码: {input_code}) 时出错: {str(e)}"
        print(error_msg)
        return f"{industry_name} 的技术指标分析：\n\n行业技术指标数据暂不可用，建议关注个股具体表现。"

class DeepSeekReasonerClient:
    """DeepSeek Reasoner模型API客户端"""
    
    def __init__(self, api_key=None):
        self.api_url = "https://api.deepseek.com/v1/chat/completions"
        self.api_key = api_key
        self.model = "deepseek-reasoner"
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        self.connected = False
    
    def connect(self):
        """连接到服务"""
        # 如果没有提供API密钥，则尝试从DeepSeekClient获取
        if not self.api_key:
            try:
                # 从现有的DeepSeekClient中获取API密钥
                temp_client = DeepSeekClient()
                self.api_key = temp_client.api_key
                
                if self.api_key and self.api_key.startswith("sk-"):
                    self.headers["Authorization"] = f"Bearer {self.api_key}"
                    self.connected = True
                    return True
                else:
                    print("无法获取有效的DeepSeek API密钥")
                    return False
            except Exception as e:
                print(f"获取DeepSeek API密钥失败: {e}")
                return False
        else:
            self.connected = True
            return True
    
    def send(self, prompt):
        """发送请求并获取回复"""
        if not self.connected:
            print("未连接到服务")
            return ""
        
        try:
            payload = {
                "model": self.model,
                "messages": [
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                "max_tokens": 65536,  # 设置为最大64K tokens
                "temperature": 0.5    # 添加temperature参数控制随机性
            }
            
            # 增加超时时间，连接超时60秒，读取超时1000秒
            response = requests.post(
                self.api_url, 
                headers=self.headers, 
                json=payload,
                timeout=(60, 1000)  # 显著增加超时时间
            )
            
            if response.status_code != 200:
                print(f"API错误: {response.status_code} - {response.text}")
                # 增加详细错误信息输出
                print(f"DeepSeek Reasoner错误码: {response.status_code}")
                print(f"完整错误响应: {response.text}")
                
                try:
                    error_data = response.json()
                    if "error" in error_data:
                        error_type = error_data['error'].get('type', '未知')
                        error_msg = error_data['error'].get('message', '无详细信息')
                        print(f"错误类型: {error_type}")
                        print(f"错误信息: {error_msg}")
                except Exception as parse_error:
                    print(f"解析错误响应失败: {parse_error}")
                
                return ""
            
            # 在解析JSON之前检查响应内容
            response_text = response.text.strip()
            if not response_text:
                print("收到空响应")
                return ""
            
            # 检查响应是否以 { 开始（JSON格式）
            if not response_text.startswith('{'):
                print(f"响应不是JSON格式，内容前100字符: {response_text[:100]}")
                return ""
            
            try:
                result = response.json()
            except json.JSONDecodeError as json_err:
                print(f"JSON解析失败: {json_err}")
                print(f"响应内容（前500字符）: {response_text[:500]}")
                return ""
            
            # 获取最终回答内容
            content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
            
            # 尝试获取推理内容（如果有）
            reasoning_content = result.get("choices", [{}])[0].get("message", {}).get("reasoning_content", "")
            if reasoning_content:
                print("收到推理内容，长度：", len(reasoning_content))
                # 记录推理内容到日志文件
                self.log_reasoning_content(prompt, reasoning_content, content)
            
            return content
        
        except Exception as e:
            print(f"发送请求失败: {e}")
            print(f"错误详情: {traceback.format_exc()}")
            return ""
    
    def log_reasoning_content(self, prompt, reasoning_content, final_content):
        """记录推理内容到日志文件"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            with open("reasoning_log.txt", "a", encoding="gbk", errors="ignore") as logfile:
                logfile.write(f"{'='*50}\n")
                logfile.write(f"时间: {timestamp}\n")
                logfile.write(f"提示词:\n{prompt}\n")
                logfile.write(f"{'-'*30} 推理内容 {'-'*30}\n")
                logfile.write(f"{reasoning_content}\n")
                logfile.write(f"{'-'*30} 最终输出 {'-'*30}\n")
                logfile.write(f"{final_content}\n")
                logfile.write(f"{'='*50}\n\n")
        except Exception as e:
            print(f"记录推理内容失败: {e}")
    
    def stream(self, prompt):
        """已弃用，改为直接使用非流式API调用"""
        print("警告: stream方法已弃用，自动切换到非流式API")
        result = self.send(prompt)
        yield result
    
    def close(self):
        """关闭连接"""
        self.connected = False

def get_akshare_news(stock_code, stock_name, max_news=10):
    """通过akshare获取股票相关新闻
    
    Args:
        stock_code: 股票代码
        stock_name: 股票名称
        max_news: 最大新闻数量
        
    Returns:
        str: 格式化的新闻信息
    """
    try:
        print(f"正在通过akshare获取股票 {stock_code}({stock_name}) 的新闻...")
        
        # 尝试获取个股新闻
        try:
            # 使用akshare获取个股新闻
            import akshare as ak
            news_df = ak.stock_news_em(symbol=stock_code)
            if not news_df.empty:
                news_list = []
                count = 0
                for _, row in news_df.iterrows():
                    if count >= max_news:
                        break
                    title = row.get('新闻标题', '无标题')
                    content = row.get('新闻内容', '无内容')[:200]  # 限制内容长度
                    publish_time = row.get('发布时间', '无时间')
                    news_list.append(f"标题：{title}\n时间：{publish_time}\n内容：{content}...")
                    count += 1
                
                if news_list:
                    formatted_news = "\n\n".join(news_list)
                    print(f"成功获取 {len(news_list)} 条akshare新闻")
                    return formatted_news
        except Exception as e:
            print(f"获取akshare个股新闻失败: {e}")
        
        # 如果个股新闻获取失败，尝试获取相关概念新闻
        try:
            # 使用股票名称搜索相关新闻
            search_df = ak.tool_trade_date_hist_sina()  # 这里应该用新闻搜索接口，但akshare接口可能有限制
            # 由于akshare新闻接口限制，这里简化处理
            print(f"akshare新闻接口受限，返回基础信息")
            return f"通过akshare查询 {stock_name}({stock_code}) 相关新闻信息受限，请依靠联网搜索获取最新资讯。"
        except Exception as e:
            print(f"获取akshare概念新闻失败: {e}")
            return f"akshare新闻获取失败：{str(e)}"
            
    except Exception as e:
        print(f"akshare新闻获取过程出错: {e}")
        return f"akshare新闻获取出错：{str(e)}"

def get_stock_news_info(stock_code, stock_name, llm_client, log_file=None, log_lock=None, max_retries=3, news_prompt_log_file="news_prompt_log.txt"):
    """获取单只股票的消息面信息（带重试机制和akshare新闻集成）
    
    Args:
        stock_code: 股票代码
        stock_name: 股票名称
        llm_client: LLM客户端
        log_file: 日志文件路径
        log_lock: 日志文件写入锁
        max_retries: 最大重试次数
        
    Returns:
        dict: 包含direction（方向）和strength（强度）的字典
    """
    # 定义特殊股票列表（这些股票可能需要更长处理时间）
    special_stocks = {
        "688981": "中芯国际",  # 半导体龙头，信息量大
        "000858": "五 粮 液",  # 白酒龙头
        "600519": "贵州茅台",  # 白酒之王
        "000001": "平安银行",  # 金融股
        "600036": "招商银行",  # 银行股
        "300015": "爱尔眼科",  # 医疗股
        # 可以根据实际情况添加更多
    }
    
    # 针对特殊股票调整参数
    if stock_code in special_stocks:
        print(f"检测到特殊股票 {stock_code}({stock_name})，调整处理策略...")
        max_retries = max(max_retries, 5)  # 至少重试5次
        print(f"  已调整最大重试次数为: {max_retries}")

    # 首先尝试获取akshare新闻
    akshare_news = get_akshare_news(stock_code, stock_name)

    # 使用统一的新版本Prompt格式（适用于所有股票）
    base_prompt = f"""我这里已经获取到了一些新闻，你也继续为我检索出最近5个交易日所有与A股的{stock_name}({stock_code})相关的新闻资讯（重点关注公司披露、公司异动、研报/投资者评价、所在行业异动等，注意需排除时效性已过和与股价无关的新闻资讯），并基于这些资讯我分析这些新闻资讯是否足以对其未来1~2天的股价产生直接利多或利空的影响。

【已获取的新闻信息】
{akshare_news}

基于我给你的和你自己找到的新闻资讯，为我提炼出最可能与近3天股票涨跌最相关的3个客观事实、其影响股价的理由、以及该客观事实发生的时间（禁止列出"行情上涨/下跌"和"资金流入/流出"这类的行情变动的"结果"，需明确其与近3天股价上涨的直接因果关系，明确该客观事实的发生时间未超时，如果不足3个，宁缺勿滥）。最后再综合已有信息判断给出结论，格式如下：

【请求格式】
第一行格式："方向：利空/利多/中性。"在"方向："后面只能回答两个字"利多"或"利空"或"中性"（如有多条利空或利多的信息，也许综合衡量后选择一个更大概率的方向）
第二行格式："强度：强/弱/中性"只能给出"强" "弱" 和"中性"这三种结果，其中"强"代表这些资讯有望全面、直接且强烈的影响情绪面进而促成上涨或下跌，"弱"代表这些资讯只会带来局部的、间接的或中长周期情绪面影响。"中性"仅在方向为"中性"时出现。
第三行起：三个关键事实、其对股价的影响的原因、该事实的发生时间。"""

    print(f"正在获取股票 {stock_code}({stock_name}) 的消息面信息（带重试机制）...")
    
    # 写入消息面专用日志的函数
    def write_news_prompt_log(prompt, response=None, attempt_num=None, error=None, success=False):
        """写入消息面专用日志文件"""
        try:
            # 使用文件锁防止并发写入冲突
            import threading
            if not hasattr(write_news_prompt_log, '_lock'):
                write_news_prompt_log._lock = threading.Lock()

            with write_news_prompt_log._lock:
                with open(news_prompt_log_file, "a", encoding="utf-8") as npf:
                    npf.write(f"{'='*100}\n")
                    npf.write(f"股票: {stock_code}({stock_name})\n")
                    if attempt_num is not None:
                        npf.write(f"尝试次数: {attempt_num}/{max_retries}\n")
                    if success:
                        npf.write(f"结果: 成功\n")
                    elif error:
                        npf.write(f"结果: 失败 - {error}\n")
                    npf.write(f"\n【发送给大模型的完整Prompt】\n")
                    npf.write(f"{'-'*80}\n")
                    npf.write(f"{prompt}\n")
                    npf.write(f"{'-'*80}\n")
                    if response:
                        npf.write(f"\n【大模型完整返回】\n")
                        npf.write(f"{'-'*80}\n")
                        npf.write(f"{response}\n")
                        npf.write(f"{'-'*80}\n")
                    elif error:
                        npf.write(f"\n【错误信息】\n")
                        npf.write(f"{error}\n")
                    npf.write(f"{'='*100}\n\n")
        except Exception as e:
            print(f"写入消息面专用日志失败: {e}")
    
    # 重试机制
    for attempt in range(max_retries):
        try:
            print(f"  尝试第 {attempt + 1}/{max_retries} 次请求...")
            
            # 记录请求开始时间
            start_time = time.time()
            
            # 发送请求到LLM
            response = llm_client.send(base_prompt)
            
            # 记录请求结束时间
            end_time = time.time()
            elapsed_time = end_time - start_time
            
            if not response:
                raise Exception("LLM返回空响应")
            
            print(f"  第 {attempt + 1} 次请求成功，耗时: {elapsed_time:.2f}秒")
            
            # 记录成功的消息面日志
            write_news_prompt_log(base_prompt, response, attempt + 1, success=True)
            
            break  # 成功则跳出重试循环
            
        except Exception as e:
            print(f"  第 {attempt + 1} 次请求失败: {e}")
            
            # 记录失败的消息面日志
            write_news_prompt_log(base_prompt, None, attempt + 1, error=str(e))
            
            if attempt == max_retries - 1:
                # 最后一次尝试失败
                print(f"股票 {stock_code} 消息面信息获取失败，已重试 {max_retries} 次")
                result = {"direction": "中性", "strength": "中性", "content": f"获取失败（重试{max_retries}次）: {str(e)}"}
                
                # 记录失败日志
                if log_file and log_lock:
                    with log_lock:
                        with open(log_file, "a", encoding="utf-8") as lf:
                            lf.write(f"{'='*80}\n")
                            lf.write(f"消息面信息获取 - {stock_code}({stock_name}) - 最终失败\n")
                            lf.write(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                            lf.write(f"重试次数: {max_retries}\n")
                            lf.write(f"最终错误: {str(e)}\n")
                            lf.write(f"解析结果: {result}\n")
                            lf.write(f"{'='*80}\n\n")
                
                return result
            else:
                # 还有重试机会，等待后继续
                if stock_code in special_stocks:
                    # 特殊股票使用更长的等待时间
                    wait_time = (attempt + 1) * 20  # 递增等待时间：20s, 40s, 60s, 80s, 100s...
                    print(f"  特殊股票等待 {wait_time} 秒后重试...")
                else:
                    wait_time = (attempt + 1) * 10  # 普通股票：10s, 20s, 30s...
                    print(f"  等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
                continue
    
    try:
        # 解析响应
        direction = "中性"
        strength = "中性"
        
        lines = response.strip().split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith("方向：") or line.startswith("方向:"):
                # 提取方向
                direction_part = line.split("：", 1) if "：" in line else line.split(":", 1)
                if len(direction_part) > 1:
                    direction_text = direction_part[1].strip().replace("。", "")
                    if direction_text in ["利多", "利空", "中性"]:
                        direction = direction_text
            elif line.startswith("强度：") or line.startswith("强度:"):
                # 提取强度
                strength_part = line.split("：", 1) if "：" in line else line.split(":", 1)
                if len(strength_part) > 1:
                    strength_text = strength_part[1].strip().replace("。", "")
                    if strength_text in ["强", "弱", "中性"]:
                        strength = strength_text
        
        print(f"股票 {stock_code}({stock_name}) 消息面信息：方向={direction}, 强度={strength}")
        
        result = {
            "direction": direction,
            "strength": strength,
            "content": response
        }
        
        # 记录成功日志（简化版，详细内容已记录在专门的消息面日志中）
        if log_file and log_lock:
            with log_lock:
                with open(log_file, "a", encoding="utf-8") as lf:
                    lf.write(f"消息面信息获取 - {stock_code}({stock_name}) - 成功\n")
                    lf.write(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}, 耗时: {elapsed_time:.2f}秒, 重试次数: {attempt + 1}\n")
                    lf.write(f"提取的方向: {direction}, 强度: {strength}\n")
                    lf.write(f"解析成功: {'是' if direction in ['利多', '利空', '中性'] and strength in ['强', '弱', '中性'] else '否'}\n")
                    lf.write(f"详细Prompt和响应请查看: {news_prompt_log_file}\n\n")
        
        return result
        
    except Exception as e:
        print(f"解析股票 {stock_code} 消息面信息时出错: {e}")
        error_result = {"direction": "中性", "strength": "中性", "content": f"解析失败: {str(e)}"}
        
        # 记录解析异常日志（简化版）
        if log_file and log_lock:
            with log_lock:
                with open(log_file, "a", encoding="utf-8") as lf:
                    lf.write(f"消息面信息解析 - {stock_code}({stock_name}) - 异常\n")
                    lf.write(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    lf.write(f"异常信息: {str(e)}\n")
                    lf.write(f"详细内容请查看: {news_prompt_log_file}\n\n")
        
        return error_result

def get_all_stocks_news_info(stocks, llm_client, max_workers=8, log_file=None, news_prompt_log_file="news_prompt_log.txt"):
    """并行获取所有股票的消息面信息
    
    Args:
        stocks: 股票列表
        llm_client: LLM客户端
        max_workers: 最大线程数，默认8
        log_file: 日志文件路径
        news_prompt_log_file: 消息面专用日志文件路径
        
    Returns:
        dict: 股票代码到消息面信息的映射
    """
    news_info_map = {}
    
    print(f"开始并行获取 {len(stocks)} 只股票的消息面信息，使用 {max_workers} 个线程...")

    # 调试：检查股票列表中是否有重复，并自动去重
    stock_codes = [stock.get('代码', '') for stock in stocks if stock.get('代码', '')]
    unique_codes = list(set(stock_codes))
    if len(stock_codes) != len(unique_codes):
        print(f"?? 警告：股票列表中有重复！总数: {len(stock_codes)}, 唯一数: {len(unique_codes)}")
        # 找出重复的股票
        from collections import Counter
        code_counts = Counter(stock_codes)
        duplicates = {code: count for code, count in code_counts.items() if count > 1}
        for code, count in duplicates.items():
            print(f"  重复股票: {code} 出现 {count} 次")

        # 自动去重：保留每只股票的第一次出现
        seen_codes = set()
        unique_stocks = []
        for stock in stocks:
            code = stock.get('代码', '')
            if code and code not in seen_codes:
                seen_codes.add(code)
                unique_stocks.append(stock)

        stocks = unique_stocks
        print(f"? 已自动去重，现在有 {len(stocks)} 只唯一股票")
    else:
        print(f"? 股票列表无重复，共 {len(stock_codes)} 只股票")

    # 创建日志锁
    log_lock = threading.Lock() if log_file else None
    
    # 写入日志头部
    if log_file and log_lock:
        with log_lock:
            with open(log_file, "a", encoding="utf-8") as lf:
                lf.write(f"\n{'#'*100}\n")
                lf.write(f"# 消息面信息获取开始 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                lf.write(f"# 共需获取 {len(stocks)} 只股票的消息面信息，使用 {max_workers} 个并行线程\n")
                lf.write(f"{'#'*100}\n\n")
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_stock = {}
        for stock in stocks:
            stock_code = stock.get('代码', '')
            stock_name = stock.get('名称', '')
            if stock_code:
                future = executor.submit(get_stock_news_info, stock_code, stock_name, llm_client, log_file, log_lock, 3, news_prompt_log_file)  # 最大重试3次，使用专门的消息面日志
                future_to_stock[future] = (stock_code, stock_name)
        
        # 收集结果
        completed = 0
        total = len(future_to_stock)
        
        for future in concurrent.futures.as_completed(future_to_stock):
            stock_code, stock_name = future_to_stock[future]
            completed += 1
            
            try:
                result = future.result()
                news_info_map[stock_code] = result
                print(f"进度: {completed}/{total} - 完成股票 {stock_code}({stock_name})")
            except Exception as e:
                print(f"获取股票 {stock_code}({stock_name}) 消息面信息失败: {e}")
                news_info_map[stock_code] = {"direction": "中性", "strength": "中性", "content": f"获取失败: {str(e)}"}
    
    # 写入日志尾部
    if log_file and log_lock:
        # 统计结果
        success_count = sum(1 for info in news_info_map.values() if info.get("direction") != "中性" or "获取失败" not in info.get("content", ""))
        failed_count = len(news_info_map) - success_count
        direction_stats = {}
        strength_stats = {}
        
        for code, info in news_info_map.items():
            direction = info.get("direction", "未知")
            strength = info.get("strength", "未知")
            direction_stats[direction] = direction_stats.get(direction, 0) + 1
            strength_stats[strength] = strength_stats.get(strength, 0) + 1
        
        with log_lock:
            with open(log_file, "a", encoding="utf-8") as lf:
                lf.write(f"\n{'#'*100}\n")
                lf.write(f"# 消息面信息获取完成 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                lf.write(f"# 总体统计:\n")
                lf.write(f"#   成功获取: {success_count} 只股票\n")
                lf.write(f"#   获取失败: {failed_count} 只股票\n")
                lf.write(f"#   方向分布: {dict(direction_stats)}\n")
                lf.write(f"#   强度分布: {dict(strength_stats)}\n")
                lf.write(f"{'#'*100}\n\n")
    
    print(f"完成获取 {len(news_info_map)} 只股票的消息面信息")
    return news_info_map

class VolcesClient:
    """火山引擎大模型API客户端"""
    
    def __init__(self, api_key="e1283034-c29c-41bc-a10f-14e32fbe828b", model="doubao-1-5-pro-32k-250115"):
        self.api_url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
        self.api_key = api_key
        self.model = model
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        self.connected = True  # 默认为已连接状态
    
    def connect(self):
        """连接到服务"""
        # 火山引擎API不需要保持连接，每次请求都是独立的HTTP调用
        # 只需检查API密钥是否存在
        if not self.api_key:
            print("API密钥不能为空")
            self.connected = False
            return False
        
        self.connected = True
        return True
    
    def send(self, prompt):
        """发送请求并获取回复"""
        if not self.connected:
            print("未连接到服务")
            return ""
        
        try:
            payload = {
                "model": self.model,
                "messages": [
                    {
                        "role": "user",
                        "content": [{"type": "text", "text": prompt}]
                    }
                ],
                "temperature": 0.5  # 设置temperature参数控制随机性
            }
            
            response = requests.post(
                self.api_url, 
                headers=self.headers, 
                json=payload,
                timeout=(30, 300)  # 设置合理的超时时间：连接30秒，读取300秒
            )
            
            if response.status_code != 200:
                print(f"API错误: {response.status_code} - {response.text}")
                # 增加详细错误信息输出
                print(f"火山引擎错误码: {response.status_code}")
                print(f"完整错误响应: {response.text}")
                
                try:
                    error_data = response.json()
                    if "error" in error_data:
                        error_type = error_data.get('error', {}).get('type', '未知')
                        error_msg = error_data.get('error', {}).get('message', '无详细信息')
                        print(f"错误类型: {error_type}")
                        print(f"错误信息: {error_msg}")
                    elif "code" in error_data:
                        error_code = error_data.get('code')
                        error_msg = error_data.get('message', '无详细信息')
                        print(f"错误代码: {error_code}")
                        print(f"错误信息: {error_msg}")
                except Exception as parse_error:
                    print(f"解析错误响应失败: {parse_error}")
                
                return ""
            
            # 在解析JSON之前检查响应内容
            response_text = response.text.strip()
            if not response_text:
                print("收到空响应")
                return ""
            
            # 检查响应是否以 { 开始（JSON格式）
            if not response_text.startswith('{'):
                print(f"响应不是JSON格式，内容前100字符: {response_text[:100]}")
                return ""
            
            try:
                result = response.json()
            except json.JSONDecodeError as json_err:
                print(f"JSON解析失败: {json_err}")
                print(f"响应内容（前500字符）: {response_text[:500]}")
                return ""
            
            # 获取回复内容 - 处理新的doubao-seed-1-6-thinking模型可能返回的格式
            content = ""
            message = result.get("choices", [{}])[0].get("message", {})
            if "content" in message:
                content = message.get("content", "")
            else:
                # 处理新模型可能返回的格式
                content_list = message.get("content", [])
                if content_list and isinstance(content_list, list):
                    for item in content_list:
                        if item.get("type") == "text":
                            content += item.get("text", "")
            
            return content
        
        except requests.exceptions.Timeout as timeout_err:
            print(f"请求超时: {timeout_err}")
            return ""
        except requests.exceptions.ConnectionError as conn_err:
            print(f"连接错误: {conn_err}")
            return ""
        except Exception as e:
            print(f"发送请求失败: {e}")
            print(f"错误详情: {traceback.format_exc()}")
            return ""
    
    def stream(self, prompt):
        """流式发送请求并获取回复"""
        if not self.connected:
            print("未连接到服务")
            return
        
        try:
            payload = {
                "model": self.model,
                "stream": True,
                "messages": [
                    {
                        "role": "user",
                        "content": [{"type": "text", "text": prompt}]
                    }
                ],
                "temperature": 0.5  # 设置temperature参数控制随机性
            }
            
            response = requests.post(
                self.api_url, 
                headers=self.headers, 
                json=payload,
                stream=True
            )
            
            if response.status_code != 200:
                print(f"API错误: {response.status_code} - {response.text}")
                yield ""
                return
            
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        data = line[6:]
                        if data.strip() == '[DONE]':
                            break
                        try:
                            json_data = json.loads(data)
                            # 处理新模型可能返回的流式格式
                            delta = json_data.get('choices', [{}])[0].get('delta', {})
                            if 'content' in delta:
                                # 旧格式
                                content = delta.get('content', '')
                                if content:
                                    yield content
                            else:
                                # 新格式 - 可能返回列表形式的content
                                content_list = delta.get('content', [])
                                if content_list and isinstance(content_list, list):
                                    for item in content_list:
                                        if item.get('type') == 'text':
                                            text = item.get('text', '')
                                            if text:
                                                yield text
                        except json.JSONDecodeError:
                            pass
        
        except Exception as e:
            print(f"流式请求失败: {e}")
            yield ""
    
    def close(self):
        """关闭连接"""
        self.connected = False
        # 实际上无需特别关闭，因为使用的是无状态HTTP请求

def find_name_column(columns):
    """查找股票名称列，支持'名称'和'名称(x)'格式"""
    # 首先检查是否有精确匹配'名称(x)'格式的列
    for col in columns:
        if isinstance(col, str) and col.startswith('名称(') and col.endswith(')'):
            return col
    
    # 其次检查是否有精确匹配'名称'的列
    for col in columns:
        if isinstance(col, str) and col == '名称':
            return col
    
    # 最后检查是否有以'名称'开头的列
    for col in columns:
        if isinstance(col, str) and col.startswith('名称'):
            return col
    
    return None

def read_stock_data(file_path):
    """读取股票数据文件，支持xls和txt格式，自动识别'名称'和'名称(x)'列"""
    try:
        # 根据文件扩展名选择读取方式
        file_ext = os.path.splitext(file_path)[1].lower()
        
        if file_ext == '.xls' or file_ext == '.xlsx':
            # 首先尝试作为文本文件读取
            try:
                print(f"尝试读取文件: {file_path}")
                with open(file_path, 'r', encoding='gbk', errors='ignore') as f:
                    lines = f.readlines()
                
                if not lines:
                    raise ValueError("文件为空")
                
                # 解析表头
                header = [col.strip() for col in lines[0].strip().split('\t') if col.strip()]
                
                # 查找名称列
                name_col = find_name_column(header)
                if not name_col:
                    raise ValueError("未找到名称列")
                
                # 解析数据
                stocks = []
                for i, line in enumerate(lines[1:], 1):
                    if not line.strip():
                        continue
                    values = [val.strip() for val in line.strip().split('\t')]
                    # 确保数据项数量与表头一致
                    if len(values) >= len(header):
                        stock = dict(zip(header, values))
                        # 确保股票代码和名称存在
                        if '代码' in stock and name_col in stock and stock['代码'] and stock[name_col]:
                            # 清理股票代码中的非法字符
                            stock['代码'] = clean_stock_code(stock['代码'])
                            # 统一名称列为'名称'
                            stock['名称'] = stock[name_col]
                            stocks.append(stock)
                    else:
                        print(f"警告: 第{i+1}行数据项数量({len(values)})小于表头数量({len(header)})，已跳过")
                
                print(f"成功读取 {len(stocks)} 只股票数据")
                return header, stocks, name_col
                
            except Exception as e:
                print(f"以文本格式读取失败: {e}，尝试以Excel格式读取...")
                # 如果文本读取失败，再尝试Excel读取
                try:
                    if file_ext == '.xlsx':
                        df = pd.read_excel(file_path, engine='openpyxl')
                    else:
                        # 对于.xls文件，尝试使用xlrd引擎
                        df = pd.read_excel(file_path, engine='xlrd')
                except Exception as e2:
                    # 尝试备选引擎
                    try:
                        engine = 'openpyxl' if file_ext == '.xlsx' else 'xlrd'
                        df = pd.read_excel(file_path, engine=engine)
                    except Exception as e3:
                        # 放弃，抛出异常
                        raise ValueError(f"无法读取文件，所有尝试都失败: {e}, {e2}, {e3}")
                
                # 查找名称列
                name_col = find_name_column(df.columns)
                if not name_col:
                    raise ValueError("未找到名称列")
                
                # 将DataFrame转换为字典列表
                stocks = []
                for _, row in df.iterrows():
                    stock = row.to_dict()
                    # 确保股票代码和名称存在
                    if '代码' in stock and name_col in stock and pd.notna(stock['代码']) and pd.notna(stock[name_col]):
                        # 清理并格式化股票代码
                        stock['代码'] = clean_stock_code(stock['代码'])
                        # 统一名称列为'名称'
                        stock['名称'] = stock[name_col]
                        stocks.append(stock)
                
                print(f"成功读取 {len(stocks)} 只股票数据")
                return df.columns.tolist(), stocks, name_col
            
        else:  # 默认为txt格式
            with open(file_path, 'r', encoding='gbk', errors='ignore') as f:
                lines = f.readlines()
            
            if not lines:
                raise ValueError("文件为空")
            
            # 解析表头
            header = [col.strip() for col in lines[0].strip().split('\t') if col.strip()]
            
            # 查找名称列
            name_col = find_name_column(header)
            if not name_col:
                raise ValueError("未找到名称列")
            
            # 解析数据
            stocks = []
            for i, line in enumerate(lines[1:], 1):
                if not line.strip():
                    continue
                values = [val.strip() for val in line.strip().split('\t')]
                # 确保数据项数量与表头一致
                if len(values) >= len(header):
                    stock = dict(zip(header, values))
                    # 确保股票代码和名称存在
                    if '代码' in stock and name_col in stock and stock['代码'] and stock[name_col]:
                        # 清理股票代码
                        stock['代码'] = clean_stock_code(stock['代码'])
                        # 统一名称列为'名称'
                        stock['名称'] = stock[name_col]
                        stocks.append(stock)
                else:
                    print(f"警告: 第{i+1}行数据项数量({len(values)})小于表头数量({len(header)})，已跳过")
            
            print(f"成功读取 {len(stocks)} 只股票数据")
            return header, stocks, name_col
    except Exception as e:
        print(f"读取文件时出错: {e}")
        raise

def clean_stock_code(code):
    """清理股票代码，去除非数字字符并确保6位格式"""
    if not code:
        return ""
    
    # 转为字符串
    code_str = str(code)
    
    # 去除所有非数字字符（如'='等）
    digits_only = ''.join(c for c in code_str if c.isdigit())
    
    # 确保6位格式，不足前面补0
    if digits_only:
        return digits_only.zfill(6)
    
    return ""

def get_historical_data(stock_code, original_headers=None, base_dir="D:\\stock\\tdxdata", current_stock=None):
    """获取股票的历史交易数据
    
    Args:
        stock_code: 股票代码
        original_headers: 原始数据文件的列名列表，用于过滤历史数据
        base_dir: 数据文件目录
        current_stock: 当前交易日的股票数据(来自股票信息部分)
    
    Returns:
        str: 仅包含格式化的资金面情况表格的字符串
    """
    try:
        if original_headers is None:
            original_headers = []
            
        # 查找符合格式的历史文件
        all_stock_files = glob.glob(os.path.join(base_dir, "全部Ａ股*.xls")) + glob.glob(os.path.join(base_dir, "全部Ａ股*.txt"))
        
        # 按照日期排序文件（从文件名中提取日期）
        def extract_date(filename):
            date_match = re.search(r'全部Ａ股(\d{8})', os.path.basename(filename))
            if date_match:
                date_str = date_match.group(1)
                return datetime.strptime(date_str, '%Y%m%d')
            return datetime.min
        
        sorted_files = sorted(all_stock_files, key=extract_date, reverse=True)
        
        # 获取当前日期，用于比较和过滤
        current_date_obj = datetime.now()
        current_date = current_date_obj.strftime('%Y%m%d')
        
        # 过滤掉可能与当前日期相同的文件
        filtered_files = []
        for file_path in sorted_files:
            file_date_match = re.search(r'全部Ａ股(\d{8})', os.path.basename(file_path))
            if file_date_match and file_date_match.group(1) != current_date:
                filtered_files.append(file_path)
        
        # 只取最近的4个文件作为历史数据(不包括当前交易日)
        recent_files = filtered_files[:4]
        
        if not recent_files:
            print(f"警告: 未找到任何历史交易数据文件")
            return ""
        
        # 存储每个交易日的日期和数据
        date_data_mapping = {}
        
        # 用于收集主力净额、主买净额、内盘、外盘
        main_flow_data = []  # 主力净额
        main_buy_data = []   # 主买净额
        inner_volume_data = [] # 内盘
        outer_volume_data = [] # 外盘
        file_dates = []
        
        # 从当前股票信息中提取当前交易日(t日)数据
        current_main_flow = None
        current_main_buy = None
        current_inner_volume = None
        current_outer_volume = None
        current_date_formatted = current_date_obj.strftime('%Y-%m-%d')
        
        if current_stock:
            for key, value in current_stock.items():
                if value and str(value).strip() and not pd.isna(value):
                    key_lower = key.lower()
                    if '主力净额' in key_lower:
                        try:
                            current_main_flow = float(str(value).replace(',', ''))
                        except (ValueError, TypeError):
                            current_main_flow = 0
                    elif '主买净额' in key_lower:
                        try:
                            current_main_buy = float(str(value).replace(',', ''))
                        except (ValueError, TypeError):
                            current_main_buy = 0
                    elif '内盘' in key_lower and '外盘' not in key_lower:
                        try:
                            current_inner_volume = float(str(value).replace(',', ''))
                        except (ValueError, TypeError):
                            current_inner_volume = 0
                    elif '外盘' in key_lower:
                        try:
                            current_outer_volume = float(str(value).replace(',', ''))
                        except (ValueError, TypeError):
                            current_outer_volume = 0
            
            # 保存当前日数据
            date_data_mapping[current_date_formatted] = {
                'main_flow': current_main_flow,
                'main_buy': current_main_buy,
                'inner_volume': current_inner_volume,
                'outer_volume': current_outer_volume
            }
        
        # 从历史数据文件中获取t-1至t-4日数据
        for file_path in recent_files:
            file_date = extract_date(file_path)
            date_str = file_date.strftime('%Y-%m-%d')
            file_dates.append(date_str)
            
            # 检查文件是否是真正的Excel文件
            is_real_excel = False
            if file_path.lower().endswith('.xls'):
                try:
                    with open(file_path, 'rb') as f:
                        header = f.read(8)
                        # 检查文件头部是否为Excel文件标识
                        is_real_excel = header.startswith(b'\xD0\xCF\x11\xE0') or header.startswith(b'PK\x03\x04')
                except Exception:
                    is_real_excel = False
            
            if is_real_excel:
                try:
                    if file_path.lower().endswith('.xls'):
                        df = pd.read_excel(file_path, engine='xlrd')
                    else:
                        df = pd.read_excel(file_path, engine='openpyxl')
                        
                    # 查找股票代码
                    stock_row = None
                    for _, row in df.iterrows():
                        if '代码' in df.columns and pd.notna(row['代码']):
                            row_code = clean_stock_code(row['代码'])
                            if row_code == stock_code:
                                stock_row = row.to_dict()
                                break
                    
                    if stock_row:
                        # 找到名称列
                        name_col = find_name_column(df.columns)
                        
                        # 收集资金面数据
                        main_flow = None
                        main_buy = None
                        inner_volume = None
                        outer_volume = None
                        
                        for key, value in stock_row.items():
                            if pd.notna(value):
                                key_lower = key.lower()
                                if '主力净额' in key_lower:
                                    try:
                                        main_flow = float(str(value).replace(',', ''))
                                    except (ValueError, TypeError):
                                        main_flow = 0
                                elif '主买净额' in key_lower:
                                    try:
                                        main_buy = float(str(value).replace(',', ''))
                                    except (ValueError, TypeError):
                                        main_buy = 0
                                elif '内盘' in key_lower and '外盘' not in key_lower:
                                    try:
                                        inner_volume = float(str(value).replace(',', ''))
                                    except (ValueError, TypeError):
                                        inner_volume = 0
                                elif '外盘' in key_lower:
                                    try:
                                        outer_volume = float(str(value).replace(',', ''))
                                    except (ValueError, TypeError):
                                        outer_volume = 0
                        
                        # 保存数据到映射表
                        date_data_mapping[date_str] = {
                            'main_flow': main_flow,
                            'main_buy': main_buy,
                            'inner_volume': inner_volume,
                            'outer_volume': outer_volume
                        }
                except Exception as e:
                    print(f"以Excel格式读取历史文件失败: {e}")
            else:
                # 不是真正的Excel文件，尝试文本方式读取
                try:
                    with open(file_path, 'r', encoding='gbk', errors='ignore') as f:
                        lines = f.readlines()
                    
                    if not lines:
                        print(f"文件 {file_path} 为空或无法读取")
                        continue
                    
                    # 解析表头
                    file_header = [col.strip() for col in lines[0].strip().split('\t') if col.strip()]
                    if not file_header:
                        print(f"文件 {file_path} 表头解析失败")
                        continue
                        
                    # 找到名称列
                    name_col = find_name_column(file_header)
                    if not name_col:
                        print(f"文件 {file_path} 中未找到名称列")
                        continue
                    
                    # 查找股票
                    stock_row = None
                    for i, line in enumerate(lines[1:], 1):
                        if not line.strip():
                            continue
                        
                        values = [val.strip() for val in line.strip().split('\t')]
                        if len(values) >= len(file_header):
                            row_data = dict(zip(file_header, values))
                            if '代码' in row_data:
                                row_code = clean_stock_code(row_data['代码'])
                                if row_code == stock_code:
                                    stock_row = row_data
                                    break
                    
                    if stock_row:
                        # 收集资金面数据
                        main_flow = None
                        main_buy = None
                        inner_volume = None
                        outer_volume = None
                        
                        for key, value in stock_row.items():
                            if value:
                                key_lower = key.lower()
                                if '主力净额' in key_lower:
                                    try:
                                        main_flow = float(str(value).replace(',', ''))
                                    except (ValueError, TypeError):
                                        main_flow = 0
                                elif '主买净额' in key_lower:
                                    try:
                                        main_buy = float(str(value).replace(',', ''))
                                    except (ValueError, TypeError):
                                        main_buy = 0
                                elif '内盘' in key_lower and '外盘' not in key_lower:
                                    try:
                                        inner_volume = float(str(value).replace(',', ''))
                                    except (ValueError, TypeError):
                                        inner_volume = 0
                                elif '外盘' in key_lower:
                                    try:
                                        outer_volume = float(str(value).replace(',', ''))
                                    except (ValueError, TypeError):
                                        outer_volume = 0
                        
                        # 保存数据到映射表
                        date_data_mapping[date_str] = {
                            'main_flow': main_flow,
                            'main_buy': main_buy,
                            'inner_volume': inner_volume,
                            'outer_volume': outer_volume
                        }
                    else:
                        print(f"在文件 {file_path} 中未找到股票 {stock_code}")
                except Exception as e:
                    print(f"以文本格式读取历史文件失败: {e}")
        
        # 生成资金面表格数据 (按新格式)
        capital_info = ""
        if date_data_mapping:
            # 按日期从新到旧排序所有日期
            sorted_dates = sorted(date_data_mapping.keys(), reverse=True)
            
            # 取最近5个交易日（如果不足5个则全部取）
            recent_dates = sorted_dates[:5]
            
            # 计算为正的天数
            positive_main_flow = 0
            positive_main_buy = 0
            
            # 构建制表符分隔格式的资金面数据（兼容calculate_fund_score_detailed函数）
            capital_info = "日期\t主力净额\t主买净额\t内盘\t外盘\t内外比\n"

            for date in recent_dates:
                data = date_data_mapping[date]
                main_flow = data['main_flow']
                main_buy = data['main_buy']
                inner_volume = data['inner_volume']
                outer_volume = data['outer_volume']

                # 计算内外盘比
                inner_outer_ratio = None
                if inner_volume is not None and outer_volume is not None and outer_volume != 0:
                    inner_outer_ratio = inner_volume / outer_volume

                # 格式化数据为制表符分隔格式
                main_flow_str = "未知"
                if main_flow is not None:
                    main_flow_str = f"{main_flow:.2f}万"
                    # 统计正值数
                    if main_flow > 0:
                        positive_main_flow += 1

                main_buy_str = "未知"
                if main_buy is not None:
                    main_buy_str = f"{main_buy:.2f}万"
                    # 统计正值数
                    if main_buy > 0:
                        positive_main_buy += 1

                inner_volume_str = "未知"
                if inner_volume is not None:
                    inner_volume_str = f"{inner_volume:.2f}万"

                outer_volume_str = "未知"
                if outer_volume is not None:
                    outer_volume_str = f"{outer_volume:.2f}万"

                ratio_str = "未知"
                if inner_outer_ratio is not None:
                    ratio_str = f"{inner_outer_ratio:.2f}"

                # 添加制表符分隔的一行
                capital_info += f"{date}\t{main_flow_str}\t{main_buy_str}\t{inner_volume_str}\t{outer_volume_str}\t{ratio_str}\n"

            # 添加总计行（用于显示）
            total_positive = positive_main_flow + positive_main_buy
            capital_info += f"---\n主力净额为正{positive_main_flow}天，主买净额为正{positive_main_buy}天，共计{total_positive}天"
        
        return capital_info
    except Exception as e:
        print(f"获取历史交易数据时出错: {e}")
        return ""

def format_stock_info(stock):
    """格式化股票信息，由于需求变更，现在总是返回空字符串"""
    # 不再返回当前行情数据
    return ""

def compare_stocks(pair_index, stock1, stock2, stock_tech_info, client, code2name, output_queue, output_file_lock, output_file, original_headers, model_log_file=None, max_retries=3, retry_delay=5, industry_tech_info=None, stock_to_industry=None, stock_resonance_info=None):
    """比较两只股票并返回结果，用于并行处理"""
    stock1_code = stock1.get('代码', '未知') or '未知'
    stock1_name = stock1.get('名称', '未知') or '未知'
    stock2_code = stock2.get('代码', '未知') or '未知'
    stock2_name = stock2.get('名称', '未知') or '未知'
    
    # 获取当前日期
    current_date_obj = datetime.now()
    current_date = current_date_obj.strftime('%Y-%m-%d')
    
    print(f"对比第{pair_index+1}组: {stock1_code}({stock1_name}) vs {stock2_code}({stock2_name})")
    
    # 从预先获取的技术面信息中获取
    tech_info1 = stock_tech_info.get(stock1_code, "")
    tech_info2 = stock_tech_info.get(stock2_code, "")
    
    # 获取资金面情况
    capital_info1 = get_historical_data(stock1_code, original_headers, current_stock=stock1)
    capital_info2 = get_historical_data(stock2_code, original_headers, current_stock=stock2)
    
    # 提取最新交易日期（如果资金面情况中包含日期）
    latest_date = ""
    date_match = re.search(r'(\d{4}-\d{2}-\d{2}):', capital_info1)
    if date_match:
        latest_date = date_match.group(1)
    
    # 使用原始历史数据获取历史行情信息（用于提取量比和换手Z）
    raw_history_info1 = ""
    raw_history_info2 = ""
    try:
        # 使用临时变量保存原始历史数据
        all_stock_files = glob.glob(os.path.join("D:\\stock\\tdxdata", "全部Ａ股*.xls")) + glob.glob(os.path.join("D:\\stock\\tdxdata", "全部Ａ股*.txt"))
        # 按日期倒序排序
        def extract_date(filename):
            date_match = re.search(r'全部Ａ股(\d{8})', os.path.basename(filename))
            if date_match:
                date_str = date_match.group(1)
                return datetime.strptime(date_str, '%Y%m%d')
            return datetime.min
        
        recent_files = sorted(all_stock_files, key=extract_date, reverse=True)[:5]  # 最近5个文件
        
        # 从这些文件中提取股票1的数据
        for file_path in recent_files:
            try:
                # 尝试读取文件
                with open(file_path, 'r', encoding='gbk', errors='ignore') as f:
                    lines = f.readlines()
                
                # 解析表头
                header = [col.strip() for col in lines[0].strip().split('\t') if col.strip()]
                
                # 在文件中查找股票1
                for line in lines[1:]:
                    values = [val.strip() for val in line.strip().split('\t')]
                    if len(values) >= len(header):
                        row_data = dict(zip(header, values))
                        if '代码' in row_data and clean_stock_code(row_data['代码']) == stock1_code:
                            # 格式化为历史数据格式
                            date_str = extract_date(file_path).strftime('%Y-%m-%d')
                            data_str = f"【{date_str}交易数据】\n"
                            for key, value in row_data.items():
                                if value:
                                    data_str += f"{key}: {value}\n"
                            raw_history_info1 += data_str + "\n\n"
                            break
                
                # 在文件中查找股票2
                for line in lines[1:]:
                    values = [val.strip() for val in line.strip().split('\t')]
                    if len(values) >= len(header):
                        row_data = dict(zip(header, values))
                        if '代码' in row_data and clean_stock_code(row_data['代码']) == stock2_code:
                            # 格式化为历史数据格式
                            date_str = extract_date(file_path).strftime('%Y-%m-%d')
                            data_str = f"【{date_str}交易数据】\n"
                            for key, value in row_data.items():
                                if value:
                                    data_str += f"{key}: {value}\n"
                            raw_history_info2 += data_str + "\n\n"
                            break
            except Exception as e:
                print(f"读取文件 {file_path} 时出错: {e}")
    except Exception as e:
        print(f"获取原始历史数据时出错: {e}")
    
    # 增强技术指标信息，添加量比和换手Z
    # 注释掉enhance_tech_info调用，因为该函数未定义
    enhanced_tech_info1 = tech_info1  # enhance_tech_info(tech_info1, stock1, raw_history_info1)
    enhanced_tech_info2 = tech_info2  # enhance_tech_info(tech_info2, stock2, raw_history_info2)
    
    # 新增: 获取行业技术指标信息
    industry_info1 = ""
    industry_info2 = ""
    industry_name1 = ""
    industry_name2 = ""
    full_industry_name1 = ""
    full_industry_name2 = ""
    
    if stock_to_industry and stock1_code in stock_to_industry:
        full_industry_name1 = stock_to_industry[stock1_code]
        # 提取连接线后面部分作为主要行业
        industry_name1 = full_industry_name1.split('-')[-1] if '-' in full_industry_name1 else full_industry_name1
        
        if industry_tech_info and industry_name1 in industry_tech_info:
            industry_info1 = industry_tech_info[industry_name1]
    
    if stock_to_industry and stock2_code in stock_to_industry:
        full_industry_name2 = stock_to_industry[stock2_code]
        # 提取连接线后面部分作为主要行业
        industry_name2 = full_industry_name2.split('-')[-1] if '-' in full_industry_name2 else full_industry_name2
        
        if industry_tech_info and industry_name2 in industry_tech_info:
            industry_info2 = industry_tech_info[industry_name2]
    
    # 新增：添加行业共振信息
    resonance_info1 = ""
    resonance_info2 = ""
    
    if stock_resonance_info and stock1_code in stock_resonance_info:
        resonance_data = stock_resonance_info[stock1_code]
        industry = resonance_data.get('industry', full_industry_name1)
        count = resonance_data.get('count', 0)
        
        if industry:
            resonance_info1 = f"\n【行业共振趋势】\n在 {industry} 行业共有 {count} 支个股在近期实现放量突破\n"
    elif full_industry_name1:
        # 没有行业共振信息但有行业信息时也输出
        resonance_info1 = f"\n【行业共振趋势】\n在 {full_industry_name1} 行业共有 0 支个股在近期实现放量突破\n"
    
    if stock_resonance_info and stock2_code in stock_resonance_info:
        resonance_data = stock_resonance_info[stock2_code]
        industry = resonance_data.get('industry', full_industry_name2)
        count = resonance_data.get('count', 0)
        
        if industry:
            resonance_info2 = f"\n【行业共振趋势】\n在 {industry} 行业共有 {count} 支个股在近期实现放量突破\n"
    elif full_industry_name2:
        # 没有行业共振信息但有行业信息时也输出
        resonance_info2 = f"\n【行业共振趋势】\n在 {full_industry_name2} 行业共有 0 支个股在近期实现放量突破\n"
    
    prompt = (
        f"以下是两个股票近期的行情信息和技术指标，请分别从以下7个方面判断哪一只股票更有可能在未来1~5天实现股价上涨，"
        f"每只股票在每个方面都需尽量寻找\"看多信号\"和\"看空信号\"(多个信号之间相对独立，避免相关性过强)，整合全部的\"看多信号\"及\"看空信号\"作为\"细分对比项\"，"
        f"在多数\"细分对比项\"上表现更优的胜出者即为该方面的胜出者(如：股票1在\"0.短线趋势判断\"的\"细分对比项\"中以4:2战胜股票2，即视为股票1在\"短线趋势判断\"这一方面中胜出)：\n\n"

        f"0. 短线趋势对比：参考\"短线趋势行情\"和\"短线技术指标\"项目数据，\"看多信号\"包括并不限于：\"DIF值拐头向上\"\"RSI6上穿50\"\"MA5拐头向上\"\"DIF值持续上涨\"\"MACD红柱持续变长\"\"MACD金叉\"\"MA5上穿MA10\"\"股价持续运行在MA5之上\"\"RSI6始终运行在50以上\"\"放量超过MAVOL5两倍以上\"\"4根及以上K线的MAVOL5上涨\"\"4根及以上K线的VOL大于MAVOL5\"\"(日线级)距压力位空间大于距支撑位空间的2倍\"\"(日线级)距压力位空间大于10%\"等。\"看空信号\"包括并不限于：\"DIF拐头向下\"\"MACD红柱连续两个周期缩短\"\"MACD死叉\"\"MA5下穿MA10\"\"RSI6下穿50\"\"突破布林带上轨后又跌破上轨\"\"DIF值持续下降\"\"3根及以上K线的MAVOL5下跌\"\"4根及以上K线的VOL小于MAVOL5\"\"(日线级)距支撑位空间大于距压力位空间的两倍\"\"(日线级)距压力位空间小于5%\"等。\n"

        f"1. 中线趋势判断：参考\"中线趋势行情\"和\"中线技术指标\"项目数据，\"看多信号\"与\"看空信号\"可参考\"0.短线趋势对比\"部分描述，但级别需改为周线级别。\n"
        
        f"2. 资金面对比：参考\"资金面情况\"项目，\"看多信号\"包括并不限于：当近5个交易日'主力净额为正'与'主买净额为正'的天数合计大于等于6天等。\"看空信号\"包括并不限于：近5个交易日'主力净额为正'与'主买净额为正'的天数合计小于6天等。\n"

        f"3. 消息面对比：参考\"最近相关新闻\"项目，\"看多信号\"包括并不限于：有直接利好股价的消息、有建议\"买入\"或\"持有\"的研报等。\"看空信号\"包括并不限于：有直接利空股价的消息、有建议\"卖出\"的研报等，如只是中性消息或对行情的客观描述，不视为看多或看空信号。\n"

        f"4. 行业趋势及共振对比：参考所属行业的\"短线趋势行情\"、\"短线技术指标\"、\"中线趋势行情\"和\"中线技术指标\"以及\"行业共振趋势\"项目，\"看多信号\"包括：行业技术面看多信号(可参考\"0.短线趋势对比\"部分描述，注意级别需改为覆盖日线级别和周线级别)以及在该行业有大于等于2支个股近期实现放量突破。\"看空信号\"包括：行业技术面看空信号以及该行业共振股票数量少于2支。\n"

        f"5. 盈亏比对比：基于缠论中枢分析计算盈亏比数值，采用双重算法取最大值。盈亏比数值更高的股票获胜，数值相等则为平局。计算考虑入笔价格、中枢上下沿、当前价格等因素。\n\n"
        
        f"以上每个方面的基于\"看多信号\"和\"看空信号\"形成的\"细分对比项\"的对比中，获胜次数多的股票视为取得该方面的胜利。\n\n"
        
        f"【输出要求】\n"
        f"1. 请不要给出总分，只需逐项列出7个方面各自的胜出者\n"
        f"2. 按0到6的顺序，依次对比每个方面\n"
        f"3. 输出格式为(严格遵守此格式)：\"X. 项目名称：胜出股票代码。理由：...\"\n"
        f"4. 如果两只股票不相上下，输出格式为(严格遵守此格式)：\"X. 项目名称：平局。理由：...\"\n"
        f"5. 胜出股票代码必须是6位数字，即{stock1_code}或{stock2_code}\n"
        f"6. 如果两只股票在\"细分对比项\"的对比中仍然胜负均等，允许判定为平局\n"
        f"7. 如果任一股票在某项指标上数据缺失或无法对比，请明确指出'由于数据缺失，无法对比该项'\n"
        f"8. 在输出的\"理由\"处，按照 \"细分对比项1：xxxxxx看多(或看空) yyyyyy看多（或看空） 细分对比项2：xxxxxx看空（或看多） yyyyyy看空（或看多） 细分对比总结果：xxxxxx x:y yyyyyy。 xxxxxxx(或yyyyyy)胜出。\" 的格式来写明该项胜负决策的具体过程\n\n"
        
        f"【股票1】\n代码: {stock1_code}\n名称: {stock1_name}\n\n"
        f"【股票1技术指标】\n{enhanced_tech_info1}\n\n"
    )
    
    # 添加资金面情况
    if capital_info1:
        prompt += f"【股票1资金面情况】\n{capital_info1}\n\n"
    
    # 添加股票1所属行业技术指标信息
    if industry_info1:
        prompt += f"【股票1所属行业技术指标】\n{industry_info1}\n"
        
        # 添加行业共振信息（直接附加到行业技术指标后面）
        if resonance_info1:
            prompt += resonance_info1 + "\n"
    
    prompt += (
        f"【股票2】\n代码: {stock2_code}\n名称: {stock2_name}\n\n"
        f"【股票2技术指标】\n{enhanced_tech_info2}\n\n"
    )
    
    # 添加资金面情况
    if capital_info2:
        prompt += f"【股票2资金面情况】\n{capital_info2}\n\n"
    
    # 添加股票2所属行业技术指标信息
    if industry_info2:
        prompt += f"【股票2所属行业技术指标】\n{industry_info2}\n"
        
        # 添加行业共振信息（直接附加到行业技术指标后面）
        if resonance_info2:
            prompt += resonance_info2 + "\n"
    
    # 添加重试机制
    retries = 0
    last_error = None
    reasoning_log_path = "reasoning_log.txt"
    
    while retries < max_retries:
        try:
            # 记录请求开始时间
            start_time = time.time()
            
            # 记录请求到日志文件
            if model_log_file:
                with output_file_lock:
                    with open(model_log_file, "a", encoding="utf-8") as logfile:
                        logfile.write(f"{'='*50}\n")
                        logfile.write(f"对比第{pair_index+1}组: {stock1_code}({stock1_name}) vs {stock2_code}({stock2_name}) - 请求时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                        logfile.write(f"提示词内容:\n{prompt}\n")
                        logfile.write(f"{'-'*50}\n")
            
            # 直接使用非流式API（即使对于支持流式API的客户端）
            # 特别处理DeepSeekReasonerClient
            if isinstance(client, DeepSeekReasonerClient):
                try:
                    # 直接使用非流式API，避免流式处理带来的问题
                    result = client.send(prompt)
                except Exception as api_error:
                    # 记录错误详情
                    error_details = f"API请求错误: {str(api_error)}\n{traceback.format_exc()}"
                    print(f"使用非流式API请求失败: {api_error}")
                    
                    # 记录错误到推理日志
                    with open(reasoning_log_path, "a", encoding="gbk", errors="ignore") as errlog:
                        errlog.write(f"{'='*50}\n")
                        errlog.write(f"时间: {datetime.now().strftime('%Y%m%d_%H%M%S')}\n")
                        errlog.write(f"错误类型: API请求错误\n")
                        errlog.write(f"对比股票: {stock1_code}({stock1_name}) vs {stock2_code}({stock2_name})\n")
                        errlog.write(f"错误详情:\n{error_details}\n")
                        errlog.write(f"提示词内容:\n{prompt}\n")
                        errlog.write(f"{'='*50}\n\n")
                    
                    result = ""
            else:
                # 对于其他客户端，优先使用非流式API
                try:
                    # 如果有send方法，直接使用
                    if hasattr(client, 'send') and callable(getattr(client, 'send')):
                        result = client.send(prompt)
                    # 如果只有stream方法，则使用流式API并收集结果
                    elif hasattr(client, 'stream') and callable(getattr(client, 'stream')):
                        full_response = []
                        for chunk in client.stream(prompt):
                            full_response.append(chunk)
                        result = ''.join(full_response)
                    else:
                        # 无法识别的客户端类型
                        print(f"无法识别的客户端类型: {type(client).__name__}")
                        result = ""
                except Exception as api_error:
                    print(f"API请求失败: {api_error}")
                    result = ""
            
            # 记录请求结束时间
            end_time = time.time()
            elapsed = end_time - start_time
            
            # 记录响应到日志
            if model_log_file:
                with output_file_lock:
                    with open(model_log_file, "a", encoding="utf-8") as logfile:
                        logfile.write(f"响应时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} (耗时: {elapsed:.2f}秒)\n")
                        logfile.write(f"模型响应:\n{result}\n")
                        logfile.write(f"{'='*50}\n\n")
            
            print(f"第{pair_index+1}组对比请求耗时: {elapsed:.2f}秒")
            
            # 检查结果是否有效
            if not result:
                retries += 1
                log_message = f"第{pair_index+1}组发送消息失败 (尝试 {retries}/{max_retries})"
                print(log_message)
                
                # 记录失败信息到推理日志
                with open(reasoning_log_path, "a", encoding="gbk", errors="ignore") as errlog:
                    errlog.write(f"{'='*50}\n")
                    errlog.write(f"时间: {datetime.now().strftime('%Y%m%d_%H%M%S')}\n")
                    errlog.write(f"错误类型: 空响应\n")
                    errlog.write(f"对比股票: {stock1_code}({stock1_name}) vs {stock2_code}({stock2_name})\n")
                    errlog.write(f"错误信息: {log_message}\n")
                    errlog.write(f"请求耗时: {elapsed:.2f}秒\n")
                    errlog.write(f"提示词内容:\n{prompt}\n")
                    errlog.write(f"{'='*50}\n\n")
                
                with output_file_lock:
                    with open(output_file, "a", encoding="utf-8") as fout:
                        fout.write(f"{log_message}\n")
                
                if retries < max_retries:
                    # 增加等待时间，使用指数退避策略
                    wait_time = retry_delay * (2 ** (retries - 1))
                    print(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                    continue
                else:
                    with output_file_lock:
                        with open(output_file, "a", encoding="utf-8") as fout:
                            fout.write(f"第{pair_index+1}组重试{max_retries}次后仍然失败\n\n")
                    return None, None, None
            
            # 清理结果 - 寻找包含七项分析的部分
            # 查找第一个"0. 超短趋势对比"的位置
            start_idx = result.find("0. 超短趋势")
            if start_idx >= 0:
                # 尝试截取到最后一个项目的结束位置
                trend_idx = result.rfind("6. 行业共振")
                if trend_idx >= 0:
                    # 寻找这段文本中的最后一个句号
                    end_idx = result.find("。", trend_idx)
                    if end_idx > 0:
                        # 截取有效部分
                        result = result[start_idx:end_idx+1]
            
            # 解析分项比较结果
            item_results = parse_comparison_results(result, stock1_code, stock2_code)
            
            # 计算总比分，只考虑有效结果（非None和非tie）
            stock1_wins = sum(1 for item, winner in item_results.items() if winner == stock1_code)
            stock2_wins = sum(1 for item, winner in item_results.items() if winner == stock2_code)
            ties = sum(1 for item, winner in item_results.items() if winner == "tie")
            valid_items = sum(1 for winner in item_results.values() if winner is not None)
            
            # 新增: 当有效对比项少于4项时，尝试重新发送请求 (从5项调整为4项，因为现在总共只有7项)
            if valid_items < 4 and retries < max_retries:
                print(f"第{pair_index+1}组有效对比项过少，仅有{valid_items}项，重新发送请求...")
                
                # 记录到错误日志文件
                with open("compare_errlog.txt", "a", encoding="utf-8") as errlog:
                    errlog.write(f"{'='*50}\n")
                    errlog.write(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    errlog.write(f"第{pair_index+1}组有效对比项过少(仅有{valid_items}项)\n")
                    errlog.write(f"对比股票: {stock1_code}({stock1_name}) vs {stock2_code}({stock2_name})\n")
                    errlog.write(f"大模型原始返回:\n{result}\n")
                    errlog.write(f"{'='*50}\n\n")
                
                # 增加重试次数
                retries += 1
                
                # 记录到日志
                log_message = f"第{pair_index+1}组有效对比项过少(仅有{valid_items}项)，重新尝试(第{retries}次)"
                print(log_message)
                
                # 增强提示词，强调必须对所有7个方面进行对比
                prompt += f"\n\n【重要提示】本次分析中只有{valid_items}个有效对比项，请务必对全部7个方面进行明确对比。即使数据不足，也请基于可用信息尽量给出对比结果，格式为\"X. 项目名称：胜出股票代码\"，或在确实无法比较时明确标记为平局。"
                
                with output_file_lock:
                    with open(output_file, "a", encoding="utf-8") as fout:
                        fout.write(f"{log_message}\n")
                    
                    # 同时记录到大模型日志
                    if model_log_file:
                        with open(model_log_file, "a", encoding="utf-8") as logfile:
                            logfile.write(f"重新发送原因: 有效对比项过少(仅有{valid_items}项)\n")
                            logfile.write(f"重新尝试次数: {retries}/{max_retries}\n")
                            logfile.write(f"增强后的提示词:\n{prompt}\n")
                            logfile.write(f"-"*50 + "\n")
                
                # 在重试前等待一段时间
                time.sleep(retry_delay)
                continue
            
            # 创建更详细的比分说明
            score_detail = f"{stock1_wins}:{stock2_wins}"
            if ties > 0:
                score_detail += f" (平局:{ties}项)"
            
            # 确定整体胜出者（只有在有比较结果的情况下）
            if valid_items > 0:
                if stock1_wins > stock2_wins:
                    winner_code = stock1_code
                elif stock2_wins > stock1_wins:
                    winner_code = stock2_code
                else:
                    # 修改: 平局情况下不随机选择，而是直接标记为平局
                    winner_code = "tie"
                    print(f"第{pair_index+1}组比分相同，结果为平局")
            else:
                # 没有有效的比较项，返回None
                winner_code = None
                print(f"第{pair_index+1}组没有有效的比较项")
            
            # 输出分数为"x:y (z有效项)"格式，增加平局信息
            score_text = f"{score_detail} ({valid_items}有效项)"
            
            # 将结果写入输出文件（使用锁确保线程安全）
            with output_file_lock:
                with open(output_file, "a", encoding="utf-8") as fout:
                    fout.write(f"第{pair_index+1}组对比\n")
                    fout.write(f"Prompt:\n{prompt}\n")
                    fout.write(f"大模型返回：{result}\n")
                    fout.write(f"分项对比结果详情：\n")
                    for item, winner in item_results.items():
                        if winner is None:
                            fout.write(f"  {item}: 未参与对比\n")
                        elif winner == "tie":
                            fout.write(f"  {item}: 平局\n")
                        else:
                            winner_name = stock1_name if winner == stock1_code else stock2_name
                            fout.write(f"  {item}: {winner}({winner_name})\n")
                    fout.write(f"比分：{stock1_code}({stock1_name}) vs {stock2_code}({stock2_name}) = {score_text}\n")
                    if winner_code == "tie":
                        fout.write(f"对比结果: 平局\n\n")
                    elif winner_code:
                        fout.write(f"胜出股票代码：{winner_code}\n\n")
                    else:
                        fout.write(f"无有效胜出者\n\n")
            
            # 在with output_file_lock:部分内添加简洁版输出
            with output_file_lock:
                # 添加明显分隔符
                with open(output_file, "a", encoding="utf-8") as fout:
                    fout.write("\n" + "="*80 + "\n")
                    fout.write(f"## 第{pair_index+1}组: {stock1_code}({stock1_name}) vs {stock2_code}({stock2_name}) ##\n")
                    fout.write("="*80 + "\n\n")
                    
                    # 仅输出大模型返回的结果
                    fout.write("【大模型分析】\n")
                    fout.write("-"*60 + "\n")
                    fout.write(f"{result}\n")
                    fout.write("-"*60 + "\n\n")
                    
                    # 输出解析后的分项结果
                    fout.write("【解析结果】\n")
                    for item, winner in item_results.items():
                        if winner is None:
                            fout.write(f"  {item}: 未参与对比\n")
                        elif winner == "tie":
                            fout.write(f"  {item}: 平局\n")
                        else:
                            winner_name = stock1_name if winner == stock1_code else stock2_name
                            fout.write(f"  {item}: {winner}({winner_name})\n")
                    fout.write("\n")
                    
                    # 最后输出总结果
                    fout.write("【总结】\n")
                    fout.write(f"比分: {score_text}\n")
                    if winner_code == "tie":
                        fout.write(f"结果: 平局\n")
                    elif winner_code:
                        winner_name = stock1_name if winner_code == stock1_code else stock2_name
                        fout.write(f"结果: {winner_code}({winner_name}) 胜出\n")
                    else:
                        fout.write(f"结果: 无有效胜出者\n")
                    
                    # 结束分隔符
                    fout.write("\n" + "="*80 + "\n")
            
            # 将结果放入队列，增加平局场次计数
            output_queue.put((pair_index, winner_code, stock1_code, stock1_name, stock2_code, stock2_name, score_text, result, ties))
            return winner_code, score_text, pair_index
            
        except Exception as e:
            last_error = e
            retries += 1
            error_msg = f"第{pair_index+1}组处理出错 (尝试 {retries}/{max_retries}): {e}"
            print(error_msg)
            
            # 记录详细的错误信息
            error_traceback = traceback.format_exc()
            with output_file_lock:
                with open(output_file, "a", encoding="utf-8") as fout:
                    fout.write(f"{error_msg}\n")
                    fout.write(f"错误详情: {error_traceback}\n\n")
                
                # 同时记录到大模型日志
                if model_log_file:
                    with open(model_log_file, "a", encoding="utf-8") as logfile:
                        logfile.write(f"错误时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                        logfile.write(f"错误信息: {e}\n")
                        logfile.write(f"错误详情: {error_traceback}\n")
                        logfile.write(f"{'='*50}\n\n")
            
            # 记录到推理日志
            with open(reasoning_log_path, "a", encoding="gbk", errors="ignore") as errlog:
                errlog.write(f"{'='*50}\n")
                errlog.write(f"时间: {datetime.now().strftime('%Y%m%d_%H%M%S')}\n")
                errlog.write(f"错误类型: 处理异常\n")
                errlog.write(f"对比股票: {stock1_code}({stock1_name}) vs {stock2_code}({stock2_name})\n")
                errlog.write(f"错误信息: {e}\n")
                errlog.write(f"错误详情:\n{error_traceback}\n")
                errlog.write(f"提示词内容:\n{prompt}\n")
                errlog.write(f"{'='*50}\n\n")
            
            if retries < max_retries:
                # 在重试前等待一段时间，使用指数退避策略
                wait_time = retry_delay * (2 ** (retries - 1))
                print(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
            else:
                with output_file_lock:
                    with open(output_file, "a", encoding="utf-8") as fout:
                        fout.write(f"第{pair_index+1}组重试{max_retries}次后仍然失败\n\n")
                return None, None, None
    
    # 所有重试都失败
    print(f"第{pair_index+1}组所有重试均失败: {last_error}")
    return None, None, None

def parse_comparison_results(result, stock1_code, stock2_code):
    """解析比较结果，提取各项的胜出者，兼容多种格式"""
    items = ["短线趋势对比", "中线趋势判断", "资金面对比",
             "消息面对比", "行业趋势及共振对比", "盈亏比对比"]
    
    # 定义每个项目可能的变体，用于更灵活的匹配
    item_variants = {
        "短线趋势对比": ["短线趋势对比", "短线趋势", "日线趋势", "短期趋势", "日级别趋势", "短线趋势判断"],
        "中线趋势判断": ["中线趋势判断", "中线趋势对比", "中线趋势", "周线趋势", "中期趋势", "周级别趋势", "多周期共振判断", "多周期共振", "共振判断", "多周期", "多周期趋势"],
        "资金面对比": ["资金面对比", "资金面", "资金情况", "主力资金", "短线资金面对比", "短线资金面"],
        "消息面对比": ["消息面对比", "消息面", "公告", "最新消息", "近期消息", "短线消息面"],
        "行业趋势及共振对比": ["行业趋势及共振对比", "行业趋势及共振", "行业趋势对比", "行业共振对比", "行业趋势", "行业共振", "行业日线趋势", "行业周线趋势", "短期行业趋势", "中期行业趋势", "行业放量", "板块共振", "共振趋势"],
        "盈亏比对比": ["盈亏比对比", "盈亏比", "中枢分析", "中枢上沿", "缠论分析", "盈亏比判断"]
    }
    
    item_results = {item: None for item in items}
    
    # 清理输入内容
    cleaned_result = result
    
    print("\n=== 开始解析大模型返回结果 ===")
    print(f"原始结果长度: {len(result)} 字符")
    
    for i, item in enumerate(items):
        item_num = i  # 从0开始编号以匹配新格式
        print(f"\n尝试解析 {item}:")
        
        # 为每个项目尝试所有可能的变体匹配
        title_match = None
        matched_pattern = None
        
        # 先尝试精确匹配标准格式
        standard_pattern = rf'{item_num}\.\s*{item}[:：]'
        title_match = re.search(standard_pattern, cleaned_result, re.IGNORECASE)
        
        # 如果标准格式匹配失败，尝试所有变体
        if not title_match and item in item_variants:
            for variant in item_variants[item]:
                variant_pattern = rf'{item_num}\.\s*{variant}[:：]'
                title_match = re.search(variant_pattern, cleaned_result, re.IGNORECASE)
                if title_match:
                    matched_pattern = variant_pattern
                    print(f"  使用变体 '{variant}' 匹配成功")
                    break
                
                # 尝试不带编号的匹配（针对大模型可能改变格式的情况）
                no_num_pattern = rf'{variant}[:：]'
                title_match = re.search(no_num_pattern, cleaned_result, re.IGNORECASE)
                if title_match:
                    matched_pattern = no_num_pattern
                    print(f"  使用无编号变体 '{variant}' 匹配成功")
                    break
        
        if title_match:
            # 找到标题后的位置
            start_pos = title_match.end()
            
            # 找到该项内容结束位置(下一个项目标题或结尾)
            end_pos = len(cleaned_result)
            
            # 寻找下一个编号项目的开始位置
            for next_idx in range(i+1, len(items)):
                next_item_num = next_idx  # 从0开始编号以匹配新格式
                next_item = items[next_idx]
                
                # 对下一个项目也考虑所有可能的变体
                next_patterns = [rf'{next_item_num}\.\s*{next_item}']
                if next_item in item_variants:
                    for variant in item_variants[next_item]:
                        next_patterns.append(rf'{next_item_num}\.\s*{variant}')
                
                # 尝试所有可能的下一项模式
                for next_pattern in next_patterns:
                    next_match = re.search(next_pattern, cleaned_result[start_pos:], re.IGNORECASE)
                    if next_match:
                        new_end_pos = start_pos + next_match.start()
                        if new_end_pos < end_pos:  # 取最近的下一个项目
                            end_pos = new_end_pos
                            break
            
            # 提取该项的内容到第一个句号
            content_to_period = cleaned_result[start_pos:end_pos]
            period_pos = content_to_period.find('。')
            if period_pos > 0:
                first_sentence = content_to_period[:period_pos].strip()
            else:
                first_sentence = content_to_period.strip()
            
            print(f"  提取内容: {first_sentence}")
            
            # 更灵活的方法：尝试多种模式匹配
            
            # 1. 仅检查冒号后是否立即出现"平局"关键词
            if re.search(r'[:：]\s*平局', first_sentence, re.IGNORECASE):
                item_results[item] = "tie"
                print(f"  检测到冒号后直接跟平局关键词")
                continue
                
            # 2. 直接模式: "项目名称：股票代码"
            direct_code_match = re.search(r'[:：]\s*(\d{6})', first_sentence)
            if direct_code_match:
                code = direct_code_match.group(1)
                if code in [stock1_code, stock2_code]:
                    item_results[item] = code
                    print(f"  直接匹配到股票代码: {code}")
                    continue
            
            # 3. 前缀模式: "项目名称：胜出股票代码xxxxxx"
            prefix_code_match = re.search(r'胜出股票代码\s*(\d{6})', first_sentence)
            if prefix_code_match:
                code = prefix_code_match.group(1)
                if code in [stock1_code, stock2_code]:
                    item_results[item] = code
                    print(f"  带前缀匹配到股票代码: {code}")
                    continue
            
            # 4. 通用模式：在冒号后到句号前找到的任何6位数字
            any_code_match = re.search(r'(\d{6})', first_sentence)
            if any_code_match:
                code = any_code_match.group(1)
                if code in [stock1_code, stock2_code]:
                    item_results[item] = code
                    print(f"  通用匹配到股票代码: {code}")
                    continue
            
            # 5. 检查胜出关键词上下文
            for code in [stock1_code, stock2_code]:
                if code in first_sentence:
                    # 检查是否在胜出上下文中
                    context_before = first_sentence[:first_sentence.find(code)].lower()
                    if any(kw in context_before for kw in ["胜出", "更优", "更强", "优于", "表现好"]):
                        item_results[item] = code
                        print(f"  通过上下文确认胜出: {code}")
                        break
            
            # 如果以上方法都不行，检查内容中是否只提到了一个股票代码
            if item_results[item] is None:
                mentioned_codes = [code for code in [stock1_code, stock2_code] if code in first_sentence]
                if len(mentioned_codes) == 1:
                    item_results[item] = mentioned_codes[0]
                    print(f"  仅提及一个股票代码，推断为胜出: {mentioned_codes[0]}")
                else:
                    # 数据缺失或无法判断，设为平局
                    item_results[item] = "tie"
                    print(f"  无法确定胜出者，设为平局")
        else:
            print(f"  未找到项目标题行，尝试直接查找项目描述")
            
            # 如果标题匹配失败，尝试在整个文本中查找这个项目的描述信息
            item_description_patterns = []
            if item in item_variants:
                for variant in item_variants[item]:
                    item_description_patterns.append(rf'{variant}[^。]*')
            
            for pattern in item_description_patterns:
                description_match = re.search(pattern, cleaned_result, re.IGNORECASE)
                if description_match:
                    description_text = description_match.group(0)
                    print(f"  找到项目描述: {description_text}")
                    
                    # 仅检查冒号后是否立即出现"平局"关键词
                    if re.search(r'[:：]\s*平局', description_text, re.IGNORECASE):
                        item_results[item] = "tie"
                        print(f"  通过描述检测到冒号后直接跟平局关键词")
                        break
                    
                    # 检查是否包含股票代码
                    for code in [stock1_code, stock2_code]:
                        if code in description_text:
                            item_results[item] = code
                            print(f"  通过描述检测到股票代码: {code}")
                            break
                    
                    if item_results[item] is not None:
                        break
    
    # 统计结果
    stock1_wins = sum(1 for winner in item_results.values() if winner == stock1_code)
    stock2_wins = sum(1 for winner in item_results.values() if winner == stock2_code)
    ties = sum(1 for winner in item_results.values() if winner == "tie")
    unknown = sum(1 for winner in item_results.values() if winner is None)
    
    print("\n=== 解析结果汇总 ===")
    print(f"股票 {stock1_code} 胜出项: {stock1_wins}")
    print(f"股票 {stock2_code} 胜出项: {stock2_wins}")
    print(f"平局项: {ties}")
    print(f"未参与对比项: {unknown}")
    
    for item, winner in item_results.items():
        if winner is None:
            print(f"{item}: 未参与对比")
        elif winner == "tie":
            print(f"{item}: 平局")
        else:
            print(f"{item}: {winner}")
    
    return item_results

def process_results_thread(output_queue, total_pairs, stock_wins, stock_rationales, code2name, stock_ties=None):
    """处理结果队列的线程函数"""
    import traceback  # 导入用于详细错误跟踪
    
    processed_count = 0
    timeout_count = 0  # 连续超时计数
    
    # 初始化平局计数
    if stock_ties is None:
        stock_ties = {}
    
    print("结果处理线程已启动，等待处理结果...")
    
    while processed_count < total_pairs:
        try:
            # 尝试获取队列中的数据
            try:
                data = output_queue.get(timeout=2)  # 增加超时时间
                timeout_count = 0  # 重置超时计数
            except Exception as queue_error:
                timeout_count += 1
                # 只有连续多次超时才输出消息，减少日志噪音
                if timeout_count % 10 == 1:  
                    print(f"等待队列数据中... ({processed_count}/{total_pairs})")
                continue
            
            # 打印获取到的数据长度，便于调试
            print(f"获取到数据，长度: {len(data)}, 已处理: {processed_count}/{total_pairs}")
            
            # 尝试解析数据，尽可能防止错误
            try:
                if len(data) >= 9:  # 新格式包含平局次数
                    pair_index = data[0]
                    winner_code = data[1]
                    stock1_code = data[2]
                    stock1_name = data[3]
                    stock2_code = data[4]
                    stock2_name = data[5]
                    score_text = data[6]
                    rationale = data[7]
                    # 忽略ties_count，我们不需要它
                else:  # 旧格式
                    pair_index = data[0]
                    winner_code = data[1]
                    stock1_code = data[2]
                    stock1_name = data[3]
                    stock2_code = data[4]
                    stock2_name = data[5]
                    score_text = data[6]
                    rationale = data[7] if len(data) > 7 else None
            except Exception as parse_error:
                print(f"解析数据错误: {parse_error}")
                print(f"数据内容: {data}")
                output_queue.task_done()  # 确保标记为完成
                continue
            
            # 确保所有涉及的股票代码都在字典中
            for code in [stock1_code, stock2_code]:
                if code and code not in stock_wins:
                    print(f"发现新股票代码 {code}，添加到股票胜出字典")
                    stock_wins[code] = 0
                    stock_rationales[code] = []
                
                if code and code not in stock_ties:
                    stock_ties[code] = 0
            
            # 处理平局情况
            if winner_code == "tie":
                print(f"第{pair_index+1}组结果为平局，比分 {score_text}")
                
                # 双方平局次数都增加
                try:
                    if stock1_code in stock_ties:
                        stock_ties[stock1_code] += 1
                    if stock2_code in stock_ties:
                        stock_ties[stock2_code] += 1
                except Exception as tie_error:
                    print(f"更新平局次数出错: {tie_error}")
                
                # 决策依据也保存
                try:
                    if rationale is not None:
                        tie_key = f"tie_{stock1_code}_{stock2_code}"
                        if tie_key not in stock_rationales:
                            stock_rationales[tie_key] = []
                        stock_rationales[tie_key].append(rationale)
                except Exception as rationale_error:
                    print(f"保存平局决策依据出错: {rationale_error}")
                
            # 处理胜出情况
            elif winner_code:
                try:
                    # 如果胜出者不在字典中，添加它
                    if winner_code not in stock_wins:
                        print(f"添加新的胜出股票 {winner_code} 到字典")
                        stock_wins[winner_code] = 0
                        stock_rationales[winner_code] = []
                    
                    winner_name = code2name.get(winner_code, "未知")
                    # 胜利次数增加
                    stock_wins[winner_code] += 1
                    # 只有当有完整的结果时才保存决策依据
                    if rationale is not None:
                        stock_rationales[winner_code].append(rationale)
                    print(f"第{pair_index+1}组胜出：{winner_code}({winner_name})，比分 {score_text}")
                except Exception as win_error:
                    print(f"处理胜出情况出错: {win_error}")
            else:
                # 处理没有胜出者的情况
                print(f"第{pair_index+1}组无胜出者，比分 {score_text}")
            
            processed_count += 1
            output_queue.task_done()
            
        except Exception as e:
            # 捕获所有可能的异常，并打印详细错误信息
            print(f"处理结果时出现严重错误:")
            print(traceback.format_exc())
            time.sleep(1)  # 添加短暂延迟，避免CPU过载
            continue
    
    print(f"结果处理线程完成，共处理 {processed_count}/{total_pairs} 组对比")
    return stock_ties

def main():
    print("=== 股票对比分析程序 ===")
    
    # 线程安全性修改说明：
    # 1. 降低并发线程数（从36降至4）减少资源竞争
    # 2. 添加技术指标获取锁机制，确保数据获取的原子性
    # 3. 防止多线程环境下股票技术指标数据被错误混淆
    
    # 调试模式：自动使用指定的文件和模型进行测试
    debug_mode = True  # 开启调试模式进行消息面日志测试
    
    # 不再生成 model_log 文件
    model_log_file = None
    clean_output_file = None
    
    # 创建推理日志文件
    reasoning_log_path = "reasoning_log.txt"
    with open(reasoning_log_path, "w", encoding="gbk", errors="ignore") as reasoning_log:
        reasoning_log.write(f"推理日志 - 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        reasoning_log.write(f"{'='*50}\n\n")
    
    print(f"已创建推理日志文件: {reasoning_log_path}")
    
    # 让用户选择大模型
    print("请选择要使用的大模型:")
    print("1. DeepSeek (本地)")
    print("2. 豆包大模型 (在线API)")
    print("3. 豆包思考大模型 (在线API)")
    print("4. DeepSeek Reasoner (推理增强模型)")
    print("5. 本地程序对比 (无需大模型)")
    print("6. 本地程序对比 (大模型搜索新闻)")

    model_choice = input("请输入选择 (1-6，默认为1): ").strip()

    # 新增：对于模式1-4，询问是否进行行业内对比
    industry_comparison_mode = False
    if model_choice in ["1", "2", "3", "4", ""]:
        industry_choice = input("是否先进行行业内对比？(y/n，默认为n): ").strip().lower()
        if industry_choice == 'y':
            industry_comparison_mode = True
            print("已启用行业内对比模式")
    
    # 默认使用DeepSeek
    news_client = None  # 初始化消息面客户端变量
    if not model_choice or model_choice == "1":
        model_type = "deepseek"
        print("正在连接DeepSeek大模型服务...")
        client = DeepSeekClient()
        if not client.connect():
            print("连接DeepSeek大模型服务失败")
            return
        print("连接DeepSeek大模型服务成功")

        # 为DeepSeek模式初始化消息面搜索客户端
        print("正在为消息面判断连接火山引擎deepseek r1联网搜索版...")
        news_client = LLMSearchClient()
        if not news_client.connect():
            print("连接火山引擎服务失败，消息面判断将使用默认逻辑")
            news_client = None
        else:
            print("消息面搜索服务连接成功")
    elif model_choice == "2":
        model_type = "doubao"
        print("正在连接豆包大模型服务...")
        client = VolcesClient(api_key="e1283034-c29c-41bc-a10f-14e32fbe828b", model="doubao-1-5-pro-32k-250115")
        if not client.connect():
            print("连接豆包大模型服务失败")
            return
        print("连接豆包大模型服务成功")

        # 为豆包大模型模式初始化消息面搜索客户端
        print("正在为消息面判断连接火山引擎deepseek r1联网搜索版...")
        news_client = LLMSearchClient()
        if not news_client.connect():
            print("连接火山引擎服务失败，消息面判断将使用默认逻辑")
            news_client = None
        else:
            print("消息面搜索服务连接成功")
    elif model_choice == "3":
        model_type = "doubao-thinking"
        print("正在连接豆包思考大模型服务...")
        client = VolcesClient(api_key="a8eefb00-e55d-4ac5-9fb9-04669e5c6385", model="doubao-seed-1-6-thinking-250615")
        if not client.connect():
            print("连接豆包思考大模型服务失败")
            return
        print("连接豆包思考大模型服务成功")

        # 为豆包思考大模型模式初始化消息面搜索客户端
        print("正在为消息面判断连接火山引擎deepseek r1联网搜索版...")
        news_client = LLMSearchClient()
        if not news_client.connect():
            print("连接火山引擎服务失败，消息面判断将使用默认逻辑")
            news_client = None
        else:
            print("消息面搜索服务连接成功")
    elif model_choice == "4":
        model_type = "deepseek-reasoner"
        print("正在连接DeepSeek Reasoner推理增强模型服务...")
        client = DeepSeekReasonerClient()
        if not client.connect():
            print("连接DeepSeek Reasoner服务失败")
            return
        print("连接DeepSeek Reasoner服务成功")

        # 为DeepSeek Reasoner模式初始化消息面搜索客户端
        print("正在为消息面判断连接火山引擎deepseek r1联网搜索版...")
        news_client = LLMSearchClient()
        if not news_client.connect():
            print("连接火山引擎服务失败，消息面判断将使用默认逻辑")
            news_client = None
        else:
            print("消息面搜索服务连接成功")
    elif model_choice == "6":
        model_type = "local-compare-news"
        client = None  # 主要使用本地评分，消息面使用专门的LLM客户端
        print("已选择本地程序对比（大模型搜索新闻）模式")
        
        # 初始化消息面搜索客户端
        print("正在连接火山引擎deepseek r1联网搜索版...")
        news_client = LLMSearchClient()
        if not news_client.connect():
            print("连接火山引擎服务失败，程序退出")
            return
        print("连接火山引擎服务成功")
    else:  # 5 本地程序对比
        model_type = "local-compare"
        client = None  # 本地模式无需大模型客户端
        news_client = None  # 不使用消息面搜索
        print("已选择本地程序对比模式，不连接任何大模型。")
    
    # 记录模型选择到日志（如果有日志文件）
    if model_log_file:
        with open(model_log_file, "a", encoding="utf-8") as logfile:
            logfile.write(f"使用的模型: {model_type}\n")
            logfile.write("="*50 + "\n\n")
    
    # 让用户输入股票数据文件路径
    file_path = input("请输入股票数据文件路径（如 D:/stock/tdxdata/个股决赛test.txt）: ").strip()
    if not file_path:
        print("未输入文件路径，程序退出")
        return
    
    # 新增：让用户输入行业共振数据文件路径（可选）
    resonance_file_path = input("请输入行业共振数据路径（可选，如 D:/stock/tdxdata/ggjs.blk ）: ").strip()
    resonance_data = {}
    if resonance_file_path:
        try:
            print(f"正在读取行业共振数据文件: {resonance_file_path}")
            # 读取行业共振数据文件
            resonance_header, resonance_stocks, resonance_name_col = read_stock_data(resonance_file_path)
            
            # 将共振股票按照一二级行业分组
            for stock in resonance_stocks:
                if '一二级行业' in stock and stock['一二级行业'] and '代码' in stock:
                    industry = stock['一二级行业']
                    code = stock['代码']
                    if industry not in resonance_data:
                        resonance_data[industry] = []
                    resonance_data[industry].append(code)
            
            print(f"成功读取行业共振数据文件，共 {len(resonance_stocks)} 只股票，{len(resonance_data)} 个行业")
        except Exception as e:
            print(f"读取行业共振数据文件失败: {e}")
            print("将继续执行但不使用行业共振数据")
            resonance_data = {}
    else:
        print("未提供行业共振数据文件，将继续执行但不使用行业共振数据")
    
    try:
        print(f"正在尝试读取文件: {file_path}")
        header, stocks, name_col = read_stock_data(file_path)
        if not stocks:
            print("没有读取到有效的股票数据")
            return
        print(f"成功读取 {len(stocks)} 只股票信息")
        
        # 打印股票代码以供检查
        for stock in stocks:
            print(f"股票: {stock['代码']} - {stock['名称']}")
        
    except Exception as e:
        print(f"读取股票数据失败: {e}")
        return
    
    # 由于已改用Zstock_tech_qmttdx.py，不再需要板块指数文件
    # 保留变量以兼容后续代码
    block_file_to_use = None
    print("使用Zstock_tech_qmttdx.py作为数据源，无需板块指数文件")
    
    # 新增: 预先获取行业和概念板块信息(只获取一次)
    print("正在获取行业和概念板块基础信息...")
    try:
        industry_df = ak.stock_board_industry_name_em()
        industry_dict = dict(zip(industry_df['板块名称'], industry_df['板块代码']))
        concept_df = ak.stock_board_concept_name_em()
        concept_dict = dict(zip(concept_df['板块名称'], concept_df['板块代码']))
        all_boards_df = pd.concat([
            industry_df.assign(板块类型='行业板块'),
            concept_df.assign(板块类型='概念板块')
        ], ignore_index=True)
        all_boards_dict = {**industry_dict, **concept_dict}
        
        # 由于改用Zstock_tech_qmttdx.py，不再从板块指数文件获取映射
        # 保留代码结构但跳过板块文件处理
                
        print(f"已获取行业和概念板块信息，共 {len(industry_dict)} 个行业， {len(concept_dict)} 个概念，总计 {len(all_boards_dict)} 个板块")
    except Exception as e:
        print(f"获取行业和概念板块信息失败: {e}, 将继续但不包含行业分析")
        industry_df = pd.DataFrame()
        concept_df = pd.DataFrame()
        all_boards_df = pd.DataFrame()
        all_boards_dict = {}
    
    # 新增: 存储行业技术指标信息
    industry_tech_info = {}
    # 新增: 存储股票到行业的映射
    stock_to_industry = {}
    # 新增: 存储股票的行业共振信息
    stock_resonance_info = {}
    
    # 在开始时先获取所有股票的技术面信息和所属行业信息
    print("开始获取全部股票的技术面信息和所属行业信息...")
    stock_tech_info = {}  # 存储技术面信息
    
    # 获取股票列表中的所有行业
    all_industries = set()
    for stock in stocks:
        if '所属行业' in stock and stock['所属行业']:
            all_industries.add(stock['所属行业'])
        elif '一二级行业' in stock and stock['一二级行业']:
            industry_full_name = stock['一二级行业']
            # 提取连接线后面部分作为主要行业
            industry_name = industry_full_name.split('-')[-1] if '-' in industry_full_name else industry_full_name
            all_industries.add(industry_name)
        elif '行业' in stock and stock['行业']:
            all_industries.add(stock['行业'])
    
    # 先预先获取所有行业的技术指标
    print(f"开始预先获取 {len(all_industries)} 个行业的技术指标...")
    for industry_name in all_industries:
        if industry_name and industry_name not in industry_tech_info:
            print(f"正在获取行业 '{industry_name}' 的技术指标...")
            try:
                # 使用锁确保行业技术指标获取的原子性
                with tech_lock:
                    industry_result = analyze_industry_properly(industry_name)
                
                # 将结果保存到行业技术指标信息字典中
                industry_tech_info[industry_name] = industry_result
                
                # 根据结果判断是否成功
                if isinstance(industry_result, str) and "行业技术指标数据暂不可用" in industry_result:
                    print(f"注意: 行业 '{industry_name}' 的技术指标获取失败，使用默认信息")
                else:
                    print(f"成功获取行业 '{industry_name}' 的技术指标")
            except Exception as ie:
                print(f"获取行业 '{industry_name}' 技术指标失败: {ie}")
                industry_tech_info[industry_name] = f"获取行业技术指标失败: {str(ie)}"
    
    # 然后获取所有股票的技术面信息和建立股票到行业的映射
    for stock in stocks:
        stock_code = stock.get('代码', '')
        if stock_code:
            try:
                print(f"获取股票 {stock_code} 的技术指标...")
                # 使用锁确保技术指标获取的原子性，防止并发冲突
                with tech_lock:
                    tech_info = stock_tech.analyze_stock(stock_code)
                    stock_tech_info[stock_code] = tech_info
                print(f"成功获取股票 {stock_code} 的技术指标")
                
                # 提取一二级行业信息并建立映射
                industry_full_name = None
                
                if '一二级行业' in stock and stock['一二级行业']:
                    industry_full_name = stock['一二级行业']
                    # 记录股票所属行业（使用完整的一二级行业名称）
                    stock_to_industry[stock_code] = industry_full_name
                    
                    # 计算行业共振信息
                    if industry_full_name in resonance_data:
                        # 获取该行业中所有共振股票
                        industry_resonance_stocks = resonance_data[industry_full_name]
                        # 统计行业共振股票数量（排除当前股票自身）
                        resonance_count = sum(1 for code in industry_resonance_stocks if code != stock_code)
                        # 存储行业共振信息
                        stock_resonance_info[stock_code] = {
                            'industry': industry_full_name,
                            'count': resonance_count
                        }
                        print(f"股票 {stock_code} 在 {industry_full_name} 行业中有 {resonance_count} 只共振股票")
                    
                    # 提取连接线后面部分作为主要行业（仅用于获取行业技术指标）
                    industry_name = industry_full_name.split('-')[-1] if '-' in industry_full_name else industry_full_name
                    
                    # 如果该行业的技术指标尚未获取，则获取
                    if industry_name and industry_name not in industry_tech_info:
                        print(f"正在获取行业 '{industry_name}' 的技术指标...")
                        try:
                            # 使用锁确保行业技术指标获取的原子性
                            with tech_lock:
                                industry_result = analyze_industry_properly(industry_name)
                            
                            # 将结果保存到行业技术指标信息字典中
                            industry_tech_info[industry_name] = industry_result
                            
                            # 根据结果判断是否成功
                            if isinstance(industry_result, str) and "行业技术指标数据暂不可用" in industry_result:
                                print(f"注意: 行业 '{industry_name}' 的技术指标获取失败，使用默认信息")
                            else:
                                print(f"成功获取行业 '{industry_name}' 的技术指标")
                        except Exception as ie:
                            print(f"获取行业 '{industry_name}' 技术指标失败: {ie}")
                            industry_tech_info[industry_name] = f"获取行业技术指标失败: {str(ie)}"
                
                # 如果没有一二级行业，尝试其他行业字段
                elif '所属行业' in stock and stock['所属行业']:
                    industry_name = stock['所属行业']
                    stock_to_industry[stock_code] = industry_name
                elif '行业' in stock and stock['行业']:
                    industry_name = stock['行业']
                    stock_to_industry[stock_code] = industry_name
                
            except Exception as e:
                print(f"获取股票 {stock_code} 的技术指标失败: {e}")
                # 出错时设为空字符串
                stock_tech_info[stock_code] = "技术指标获取失败，但这不影响对比。"
    
    print(f"完成获取 {len(stock_tech_info)} 只股票的技术面信息")
    print(f"完成获取 {len(industry_tech_info)} 个行业的技术面信息")
    print(f"建立了 {len(stock_to_industry)} 只股票到行业的映射")
    print(f"获取了 {len(stock_resonance_info)} 只股票的行业共振信息")
    
    # 新增：获取消息面信息（仅在模式5-6中执行）
    stock_news_info = {}
    if news_client is not None and model_type not in ["deepseek", "doubao", "doubao-thinking", "deepseek-reasoner"]:
        print("\n开始获取所有股票的消息面信息...")
        # 根据模型类型创建对应的日志文件
        if model_type == "local-compare-news":
            news_log_file = "compare_local_news.txt"
        else:
            news_log_file = "compare_news.txt"

        # 初始化消息面专用日志文件
        news_prompt_log_file = "news_prompt_log.txt"
        try:
            with open(news_prompt_log_file, "w", encoding="utf-8") as npf:
                npf.write(f"消息面Prompt和响应日志\n")
                npf.write(f"程序启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                npf.write(f"使用模型: {model_type}\n")
                npf.write(f"{'='*100}\n\n")
            print(f"已初始化消息面专用日志文件: {news_prompt_log_file}")
        except Exception as e:
            print(f"初始化消息面日志文件失败: {e}")

        stock_news_info = get_all_stocks_news_info(stocks, news_client, max_workers=8, log_file=news_log_file, news_prompt_log_file=news_prompt_log_file)
        print(f"完成获取 {len(stock_news_info)} 只股票的消息面信息")
    else:
        if model_type in ["deepseek", "doubao", "doubao-thinking", "deepseek-reasoner"]:
            print("\n模式1-4暂时不获取消息面信息（根据用户要求，消息面对比默认为平局）")
        else:
            print("\n未配置消息面搜索客户端，跳过消息面信息获取")

    # 新增：基本面数据缓存 - 一次性获取所有股票的基本面数据
    print("\n开始预缓存所有股票的基本面数据...")
    fundamental_cache = {}
    start_time = time.time()

    # 使用并行方式获取基本面数据
    with concurrent.futures.ThreadPoolExecutor(max_workers=8) as fundamental_executor:
        future_to_code = {}
        for stock in stocks:
            stock_code = stock['代码']
            future = fundamental_executor.submit(get_fundamental_data_for_comparison, stock_code)
            future_to_code[future] = stock_code

        # 收集结果
        for future in concurrent.futures.as_completed(future_to_code):
            stock_code = future_to_code[future]
            try:
                fundamental_data = future.result()
                fundamental_cache[stock_code] = fundamental_data
                print(f"  完成股票 {stock_code} 基本面数据缓存")
            except Exception as e:
                print(f"  股票 {stock_code} 基本面数据获取失败: {e}")
                fundamental_cache[stock_code] = f"股票 {stock_code} 基本面数据获取失败: {str(e)}"

    end_time = time.time()
    print(f"基本面数据缓存完成，耗时: {end_time - start_time:.2f}秒，成功缓存 {len(fundamental_cache)} 只股票")

    # 初始化股票相关变量（在行业内对比之前先初始化）
    # 构建股票代码到名称的映射
    code2name = {stock['代码']: stock['名称'] for stock in stocks}

    # 初始化每只股票的胜出次数
    stock_wins = {stock['代码']: 0 for stock in stocks}

    # 新增: 初始化每只股票的平局次数
    stock_ties = {stock['代码']: 0 for stock in stocks}

    # 保存决策依据
    stock_rationales = {stock['代码']: [] for stock in stocks}
    
    # 创建输出文件（轮流使用两个文件）
    output_file = get_alternating_log_file()
    print(f"本次运行将使用日志文件: {output_file}")
    with open(output_file, "w", encoding="utf-8") as fout:
        fout.write(f"# 股票对比分析日志\n")
        fout.write(f"# 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        fout.write(f"# 日志文件: {output_file}\n")
        fout.write(f"{'='*80}\n\n")
    
    # ===== 本地程序对比初始化 =====
    # 为所有模型类型都进行本地评分计算（用于消息面判断）
    stock_item_scores_map = {}
    stock_extra_map = {}
    stock_raw_info_map = {}

    if model_type in ["local-compare", "local-compare-news"]:
        local_log_file = "compare_local.txt" if model_type == "local-compare" else "compare_local_news.txt"
        with open(local_log_file, "w", encoding="utf-8") as lf:
            log_title = "本地程序股票对比日志" if model_type == "local-compare" else "本地程序股票对比日志（大模型搜索新闻版）"
            lf.write(f"# {log_title}\n生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        print("开始本地评分计算，以便后续对比 ...")
    else:
        # 其他模型类型也需要本地评分计算（但模式1-4跳过消息面）
        if model_type in ["deepseek", "doubao", "doubao-thinking", "deepseek-reasoner"]:
            print("开始本地评分计算（模式1-4跳过消息面判断）...")
        else:
            print("开始本地评分计算（用于消息面判断）...")

    for idx_stock, stock in enumerate(stocks):
        code_tmp = stock['代码']
        tech_tmp = stock_tech_info.get(code_tmp, "")
        history_tmp = stock_score.get_historical_data(code_tmp, header, current_stock=stock)
        industry_tmp = ""
        if code_tmp in stock_to_industry:
            ind_full_tmp = stock_to_industry[code_tmp]
            ind_nm_tmp = ind_full_tmp.split('-')[-1] if '-' in ind_full_tmp else ind_full_tmp
            industry_tmp = industry_tech_info.get(ind_nm_tmp, "")
        res_cnt_tmp = stock_resonance_info.get(code_tmp, {}).get('count', 0)
        news_tmp = stock_news_info.get(code_tmp, {})  # 新增：获取消息面信息

        # 保存用于本地对比的原始信息
        stock_raw_info_map[code_tmp] = {
            "tech_info": tech_tmp,
            "history_info": history_tmp,
            "industry_info": industry_tmp,
            "resonance_count": res_cnt_tmp,
            "news_info": news_tmp  # 新增：保存消息面信息
        }

        # 模式1-4跳过消息面计算
        skip_news_calculation = model_type in ["deepseek", "doubao", "doubao-thinking", "deepseek-reasoner"]
        item_scores_tmp, extra_tmp = compute_local_item_scores(stock, tech_tmp, history_tmp, industry_tmp, res_cnt_tmp, news_tmp, skip_news=skip_news_calculation)
        stock_item_scores_map[code_tmp] = item_scores_tmp
        stock_extra_map[code_tmp] = extra_tmp
        if (idx_stock + 1) % 20 == 0 or idx_stock == len(stocks) - 1:
            print(f"  已完成 {idx_stock + 1}/{len(stocks)} 只股票评分")
    # ===== 本地程序对比初始化结束 =====
    
    # 创建线程安全的输出队列和文件锁
    output_queue = Queue()
    output_file_lock = threading.Lock()
    
    # ===== 行业对比初始化 =====
    # 只有模式5和6需要进行行业对比初始化
    if model_type in ["local-compare", "local-compare-news"]:
        # 提取所有涉及的行业
        all_industries_in_stocks = set()
        industry_to_resonance_count = {}

        for stock in stocks:
            stock_code = stock.get('代码', '')
            if stock_code in stock_to_industry:
                full_industry_name = stock_to_industry[stock_code]
                # 提取连接线后面部分作为主要行业
                industry_name = full_industry_name.split('-')[-1] if '-' in full_industry_name else full_industry_name
                all_industries_in_stocks.add(industry_name)

                # 统计每个行业的共振股票数量
                if stock_code in stock_resonance_info:
                    resonance_count = stock_resonance_info[stock_code]['count']
                    if industry_name not in industry_to_resonance_count:
                        industry_to_resonance_count[industry_name] = resonance_count
                    else:
                        # 使用最大值（理论上同一行业的共振数应该相同）
                        industry_to_resonance_count[industry_name] = max(
                            industry_to_resonance_count[industry_name], resonance_count
                        )

        # 过滤掉没有技术指标的行业
        valid_industries = []
        for industry_name in all_industries_in_stocks:
            if industry_name in industry_tech_info and industry_tech_info[industry_name]:
                # 检查是否包含有效技术指标（不是错误信息）
                tech_info = industry_tech_info[industry_name]
                if not ("行业技术指标数据暂不可用" in tech_info or "获取行业技术指标失败" in tech_info):
                    valid_industries.append(industry_name)

        print(f"参与行业对比的行业数量: {len(valid_industries)}")
        for industry in valid_industries:
            resonance_count = industry_to_resonance_count.get(industry, 0)
            print(f"  行业: {industry}, 共振股票数: {resonance_count}")

        # 行业对比相关变量初始化
        industry_pairs = []
        industry_wins = {}
        industry_ties = {}
        industry_rationales = {}
        industry_item_scores_map = {}
        industry_extra_map = {}
        industry_raw_info_map = {}

        if len(valid_industries) >= 2:
            # 生成行业对比组合
            industry_pairs = list(itertools.combinations(valid_industries, 2))
            industry_pair_count = len(industry_pairs)
            print(f"共有 {industry_pair_count} 个行业对比组合")

            # 初始化行业胜出和平局次数
            for industry in valid_industries:
                industry_wins[industry] = 0
                industry_ties[industry] = 0
                industry_rationales[industry] = []

            # 为每个行业计算评分
            print("开始计算行业评分...")
            for industry_name in valid_industries:
                tech_info = industry_tech_info[industry_name]
                resonance_count = industry_to_resonance_count.get(industry_name, 0)

                # 保存原始信息用于行业对比日志
                industry_raw_info_map[industry_name] = {
                    "tech_info": tech_info,
                    "resonance_count": resonance_count
                }

                # 计算行业评分
                item_scores, extra = compute_industry_item_scores(industry_name, tech_info, resonance_count)
                industry_item_scores_map[industry_name] = item_scores
                industry_extra_map[industry_name] = extra

                print(f"  完成行业 {industry_name} 评分计算")

            # 创建行业对比日志文件
            industry_log_file = "compare_industry_local.txt"
            with open(industry_log_file, "w", encoding="utf-8") as ilf:
                ilf.write(f"# 行业对比分析日志\n生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            print("行业对比初始化完成")
        else:
            industry_pair_count = 0
            industry_log_file = None
            print("可用行业少于2个，跳过行业对比")
    else:
        # 模式1-4不进行行业对比，初始化空变量
        print("当前模式不进行行业对比，跳过行业对比初始化")
        all_industries_in_stocks = set()
        industry_to_resonance_count = {}
        valid_industries = []
        industry_pairs = []
        industry_wins = {}
        industry_ties = {}
        industry_rationales = {}
        industry_item_scores_map = {}
        industry_extra_map = {}
        industry_raw_info_map = {}
        industry_pair_count = 0
        industry_log_file = None

    # 注意：result_thread 的启动需要在 pair_count 定义之后
    
    # 新增：行业内对比逻辑（仅适用于模式1-4）
    industry_champions = []  # 存储各行业冠军
    industry_battle_records = {}  # 存储各行业内部对比的详细战绩

    if industry_comparison_mode and model_type in ["deepseek", "doubao", "doubao-thinking", "deepseek-reasoner"]:
        print("\n=== 开始行业内对比 ===")

        # 按二级行业分组股票
        industry_groups = {}
        for stock in stocks:
            if '一二级行业' in stock and stock['一二级行业']:
                industry_full_name = stock['一二级行业']
                if industry_full_name not in industry_groups:
                    industry_groups[industry_full_name] = []
                industry_groups[industry_full_name].append(stock)

        print(f"共发现 {len(industry_groups)} 个二级行业分组")

        # 对每个有2只以上股票的行业进行内部对比
        for industry_name, industry_stocks in industry_groups.items():
            if len(industry_stocks) >= 2:
                print(f"\n--- 行业内对比：{industry_name} ({len(industry_stocks)}只股票) ---")

                # 生成该行业内的股票对比组合
                industry_pairs = list(itertools.combinations(industry_stocks, 2))
                industry_pair_count = len(industry_pairs)
                print(f"  行业内对比组合数：{industry_pair_count}")

                # 初始化该行业内股票的胜负统计
                industry_stock_wins = {stock['代码']: 0 for stock in industry_stocks}
                industry_stock_ties = {stock['代码']: 0 for stock in industry_stocks}

                # 创建该行业的输出队列和结果处理线程
                industry_output_queue = Queue()
                industry_result_thread = threading.Thread(
                    target=process_results_thread,
                    args=(industry_output_queue, industry_pair_count, industry_stock_wins, {}, code2name, industry_stock_ties)
                )
                industry_result_thread.daemon = True
                industry_result_thread.start()

                # 执行该行业内的股票对比
                industry_max_workers = min(os.cpu_count() or 4, 4)  # 行业内对比使用较少的线程
                with concurrent.futures.ThreadPoolExecutor(max_workers=industry_max_workers) as industry_executor:
                    industry_futures = []
                    for i, pair in enumerate(industry_pairs):
                        stock1, stock2 = pair
                        future = industry_executor.submit(
                            compare_stocks_modular_industry, i, stock1, stock2, stock_tech_info,
                            client, code2name, industry_output_queue, output_file_lock, output_file, header, model_log_file,
                            max_retries=3, retry_delay=5,
                            industry_tech_info=industry_tech_info, stock_to_industry=stock_to_industry, stock_resonance_info=stock_resonance_info,
                            debug_mode=debug_mode, stock_item_scores_map=stock_item_scores_map, stock_extra_map=stock_extra_map,
                            fundamental_cache=fundamental_cache, model_type=model_type, industry_name=industry_name
                        )
                        industry_futures.append(future)

                    # 等待该行业内对比完成
                    for future in concurrent.futures.as_completed(industry_futures):
                        try:
                            future.result()
                        except Exception as e:
                            print(f"  行业内对比失败: {e}")

                # 等待该行业结果处理完成
                industry_output_queue.join()

                # 确定该行业的冠军（可能有并列）
                max_wins = max(industry_stock_wins.values()) if industry_stock_wins else 0
                industry_champions_in_group = [stock for stock in industry_stocks
                                             if industry_stock_wins[stock['代码']] == max_wins]

                # 保存该行业的详细战绩
                industry_battle_records[industry_name] = {
                    'stocks': industry_stocks,
                    'wins': industry_stock_wins.copy(),
                    'ties': industry_stock_ties.copy(),
                    'champions': industry_champions_in_group.copy()
                }

                print(f"  行业 {industry_name} 冠军：")
                for champion in industry_champions_in_group:
                    champion_code = champion['代码']
                    champion_name = champion['名称']
                    wins = industry_stock_wins[champion_code]
                    ties = industry_stock_ties[champion_code]
                    print(f"    {champion_code}({champion_name}) - 胜出:{wins}, 平局:{ties}")
                    industry_champions.append(champion)
            else:
                # 不足2只股票的行业，直接将股票加入冠军列表
                print(f"  行业 {industry_name} 只有 {len(industry_stocks)} 只股票，直接晋级")
                industry_champions.extend(industry_stocks)
                # 也保存到战绩记录中
                industry_battle_records[industry_name] = {
                    'stocks': industry_stocks,
                    'wins': {stock['代码']: 0 for stock in industry_stocks},
                    'ties': {stock['代码']: 0 for stock in industry_stocks},
                    'champions': industry_stocks.copy()
                }

        print(f"\n=== 行业内对比完成，共产生 {len(industry_champions)} 只行业冠军股票 ===")

        # 更新股票列表为行业冠军
        stocks = industry_champions
        print("接下来将对各行业冠军进行最终对比")

        # 重新构建相关映射
        code2name = {stock['代码']: stock['名称'] for stock in stocks}

        # 重新初始化胜负统计（只针对行业冠军）
        stock_wins = {stock['代码']: 0 for stock in stocks}
        stock_ties = {stock['代码']: 0 for stock in stocks}
        stock_rationales = {stock['代码']: [] for stock in stocks}

    # 构建股票对比组合（在行业内对比后，stocks可能已经更新为行业冠军）
    stock_pairs = list(itertools.combinations(stocks, 2))
    pair_count = len(stock_pairs)

    # 调试模式：限制对比数量并添加特殊测试
    print(f"共有 {pair_count} 个股票对比组合")

    # 启动结果处理线程，传入平局次数字典
    result_thread = threading.Thread(
        target=process_results_thread,
        args=(output_queue, pair_count, stock_wins, stock_rationales, code2name, stock_ties)
    )
    result_thread.daemon = True
    result_thread.start()

    # 获取CPU核心数，决定线程数量
    max_workers = min(os.cpu_count() or 12, 12)  # 将最大线程数限制为12，提升并行效率
    print(f"使用 {max_workers} 个并行线程处理对比任务")

    # 使用线程池并行处理股票对比
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        futures = []
        for i, pair in enumerate(stock_pairs):
            stock1, stock2 = pair
            if model_type in ["local-compare", "local-compare-news"]:
                future = executor.submit(
                    compare_stocks_local, i, stock1, stock2, stock_item_scores_map, stock_extra_map,
                    stock_raw_info_map, output_queue, output_file_lock, local_log_file
                )
            else:
                future = executor.submit(
                    compare_stocks_modular, i, stock1, stock2, stock_tech_info,
                    client, code2name, output_queue, output_file_lock, output_file, header, model_log_file,
                    max_retries=3, retry_delay=5,
                    industry_tech_info=industry_tech_info, stock_to_industry=stock_to_industry, stock_resonance_info=stock_resonance_info,
                    debug_mode=debug_mode, stock_item_scores_map=stock_item_scores_map, stock_extra_map=stock_extra_map,
                    fundamental_cache=fundamental_cache, model_type=model_type
                )
            futures.append(future)
        
        # 等待所有任务完成并显示进度
        total = len(futures)
        completed = 0
        failed = 0
        
        for future in concurrent.futures.as_completed(futures):
            completed += 1
            try:
                result = future.result()
                if result[0] is None:  # 如果分析失败
                    failed += 1
                    
                    # 每5个失败的请求，检查并尝试重新连接
                    if failed > 0 and failed % 5 == 0:
                        print(f"检测到连续失败的请求，尝试检查连接...")
                        if not check_and_reconnect(client):
                            print("无法恢复连接，程序将继续尝试处理剩余任务")
            except Exception as e:
                print(f"处理任务结果时出错: {e}")
                failed += 1
            
            # 显示进度
            if completed % 10 == 0 or completed == total:
                success_rate = ((completed - failed) / completed * 100) if completed > 0 else 0
                print(f"进度: {completed}/{total} ({(completed/total*100):.1f}%)，成功率: {success_rate:.1f}%")
    
    # 等待结果处理线程完成
    output_queue.join()
    
    # ===== 执行行业对比 =====
    # 只有模式5和6需要进行行业对比
    if (model_type in ["local-compare", "local-compare-news"]) and len(valid_industries) >= 2:
        print(f"\n开始执行行业对比，共 {industry_pair_count} 组...")

        # 创建行业对比专用的结果队列和处理线程
        industry_output_queue = Queue()

        # 启动行业结果处理线程
        industry_result_thread = threading.Thread(
            target=process_results_thread,
            args=(industry_output_queue, industry_pair_count, industry_wins, industry_rationales, {}, industry_ties)
        )
        industry_result_thread.daemon = True
        industry_result_thread.start()

        # 执行行业对比（单线程执行，因为是本地计算，速度很快）
        completed_industry_pairs = 0
        for i, (industry1, industry2) in enumerate(industry_pairs):
            try:
                result = compare_industries_local(
                    i, industry1, industry2,
                    industry_item_scores_map, industry_extra_map, industry_raw_info_map,
                    industry_output_queue, output_file_lock, industry_log_file
                )
                completed_industry_pairs += 1
                if (completed_industry_pairs) % 10 == 0 or completed_industry_pairs == industry_pair_count:
                    print(f"行业对比进度: {completed_industry_pairs}/{industry_pair_count}")
            except Exception as e:
                print(f"行业对比第{i+1}组失败: {e}")
                completed_industry_pairs += 1

        # 等待行业结果处理线程完成
        industry_output_queue.join()

        print(f"行业对比完成，共处理 {completed_industry_pairs}/{industry_pair_count} 组")
    
    # 输出股票排名 - 修改为考虑平局次数
    print("\n=== 股票排名 ===")
    # 按照胜场和平局场排序
    sorted_stocks = sorted(
        [(code, wins, stock_ties[code]) for code, wins in stock_wins.items()], 
        key=lambda x: (x[1], x[2]), 
        reverse=True
    )
    
    for rank, (code, wins, ties) in enumerate(sorted_stocks, 1):
        name = code2name.get(code, "未知")
        print(f"第{rank}名: 股票代码 {code}({name})，胜出次数: {wins}，平局次数: {ties}")

    # 如果进行了行业内对比，输出各行业内部对比的详细战绩
    if industry_comparison_mode and industry_battle_records:
        print("\n=== 各行业内部对比详细战绩 ===")
        for industry_name, battle_record in industry_battle_records.items():
            print(f"\n【{industry_name}】")
            stocks_in_industry = battle_record['stocks']
            wins_in_industry = battle_record['wins']
            ties_in_industry = battle_record['ties']
            champions_in_industry = battle_record['champions']

            if len(stocks_in_industry) >= 2:
                # 按胜负排序显示该行业内所有股票的战绩
                industry_sorted = sorted(
                    [(stock['代码'], wins_in_industry[stock['代码']], ties_in_industry[stock['代码']], stock['名称'])
                     for stock in stocks_in_industry],
                    key=lambda x: (x[1], x[2]),
                    reverse=True
                )

                for i, (code, wins, ties, name) in enumerate(industry_sorted, 1):
                    is_champion = any(champ['代码'] == code for champ in champions_in_industry)
                    champion_mark = " ★" if is_champion else ""
                    print(f"  {i}. {code}({name}) - 胜出:{wins}, 平局:{ties}{champion_mark}")
            else:
                # 只有1只股票的行业
                stock = stocks_in_industry[0]
                print(f"  1. {stock['代码']}({stock['名称']}) - 直接晋级 ★")

    # 输出行业排名
    # 只有模式5和6显示行业排名
    if model_type in ["local-compare", "local-compare-news"]:
        print("\n=== 行业排名 ===")
        if len(valid_industries) >= 2:
            # 修改排名规则：按共振股票数量排序，而不是按对比胜负
            sorted_industries = sorted(
                [(industry, industry_to_resonance_count.get(industry, 0), industry_wins.get(industry, 0), industry_ties.get(industry, 0))
                 for industry in valid_industries],
                key=lambda x: x[1],  # 按共振股票数量排序
                reverse=True
            )

            for rank, (industry, resonance_count, wins, ties) in enumerate(sorted_industries, 1):
                print(f"第{rank}名: 行业 {industry}，共振股票数: {resonance_count}，胜出次数: {wins}，平局次数: {ties}")

            # 输出行业对比的汇总信息
            print(f"\n行业对比汇总:")
            print(f"  参与对比的行业数量: {len(valid_industries)}")
            print(f"  总行业对比组合: {industry_pair_count}")
            print(f"  行业排名规则: 按共振股票数量排序")
            print(f"  行业对比维度: 超短趋势、短线趋势、中线趋势、资金面、消息面、行业共振、基本面、盈亏比")
            print(f"  行业对比日志文件: compare_industry_local.txt")

            # 新增功能：前5名行业内股票对比排名
            if len(sorted_industries) > 0:
                # 转换格式以兼容原有函数
                sorted_industries_compat = [(industry, wins, ties) for industry, _, wins, ties in sorted_industries]
                process_industry_stock_rankings(sorted_industries_compat, stock_to_industry, stock_item_scores_map,
                                               stock_extra_map, stock_raw_info_map, code2name)
        else:
            print("可用行业少于2个，未进行行业对比")
    else:
        # 模式1-4不显示行业排名
        print("\n=== 行业排名 ===")
        print("当前模式不进行行业对比和排名")
    
    # 输出前5名的决策依据并进行深度分析 - 仅在非调试模式下执行
    if not debug_mode:
        print("\n=== 前5名股票的决策依据综合分析 ===")
        for rank, (code, wins, ties) in enumerate(sorted_stocks[:5], 1):
            if rank > 5:
                break
            name = code2name.get(code, "未知")
            print(f"\n第{rank}名: 股票代码 {code}({name})，胜出次数: {wins}，平局次数: {ties}")
            
            if stock_rationales[code]:
                # 将所有的决策依据汇总发送给大模型进行分析
                all_rationales = "\n\n---\n\n".join(stock_rationales[code])
                analysis_prompt = f"""
请分析以下股票代码 {code}({name}) 在多次对比中胜出的所有理由，提取出所有对比中的关键因素，
并计算每个因素单项取胜或落败的次数。请从技术面、基本面、市场情绪等多个维度进行分析。
格式要求：
1. 首先列出所有发现的关键因素
2. 然后计算每个因素在各次对比中的胜负次数
3. 最后给出该股票的综合优势分析

以下是该股票在{len(stock_rationales[code])}次对比中胜出的所有理由：

{all_rationales}
"""
                print("正在请求大模型进行综合分析...")
                analysis_result = client.send(analysis_prompt)
                print(f"股票{code}({name})的因素分析:\n{analysis_result}")
            else:
                print("无决策依据")
    


# ========================= 行业内股票对比功能 =========================

def compare_stocks_within_industry(industry_name, industry_stocks, stock_item_scores_map, stock_extra_map, stock_raw_info_map, code2name, log_file_path):
    """
    对指定行业内的股票进行两两对比，返回胜负统计
    """
    if len(industry_stocks) < 2:
        return {}, {}
    
    # 初始化胜负统计
    stock_wins = {}
    stock_ties = {}
    stock_rationales = {}
    
    for stock_code in industry_stocks:
        stock_wins[stock_code] = 0
        stock_ties[stock_code] = 0
        stock_rationales[stock_code] = []
    
    # 创建股票对比组合
    stock_pairs = list(itertools.combinations(industry_stocks, 2))
    
    print(f"  行业 {industry_name} 内股票对比: {len(industry_stocks)} 只股票，{len(stock_pairs)} 组对比")
    
    # 创建对比日志文件（如果指定了路径）
    if log_file_path:
        try:
            with open(log_file_path, "w", encoding="utf-8") as log_file:
                log_file.write(f"行业 {industry_name} 内股票对比日志 - 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                log_file.write(f"{'='*50}\n\n")
        except Exception as e:
            print(f"  创建日志文件失败: {e}")
    else:
        print(f"  跳过创建日志文件（log_file_path为None）")
    
    # 使用队列和锁（简化版，单线程处理）
    output_queue = Queue()
    output_file_lock = threading.Lock()
    
    # 逐对比较
    for pair_index, (code1, code2) in enumerate(stock_pairs):
        # 构造股票对象
        stock1 = {'代码': code1, '名称': code2name.get(code1, '未知')}
        stock2 = {'代码': code2, '名称': code2name.get(code2, '未知')}
        
        # 使用本地对比函数
        compare_stocks_local(pair_index, stock1, stock2, stock_item_scores_map, 
                           stock_extra_map, stock_raw_info_map, output_queue, 
                           output_file_lock, log_file_path)
    
    # 处理对比结果
    while not output_queue.empty():
        result = output_queue.get()
        # compare_stocks_local返回的格式: (pair_index, overall_winner, code1, name1, code2, name2, score, comparison_type, ties)
        if len(result) >= 9:
            pair_index, overall_winner, code1, name1, code2, name2, score, comparison_type, ties_count = result
            
            # 更新胜负统计
            if overall_winner == "tie":
                # 平局，两方都加平局次数
                if code1 in stock_ties:
                    stock_ties[code1] += 1
                if code2 in stock_ties:
                    stock_ties[code2] += 1
            elif overall_winner in stock_wins:
                stock_wins[overall_winner] += 1
                # 记录胜出理由（简化版）
                rationale = f"在第{pair_index+1}组对比中胜出，比分: {score}"
                stock_rationales[overall_winner].append(rationale)
    
    return stock_wins, stock_ties


def process_industry_stock_rankings(sorted_industries, stock_to_industry, stock_item_scores_map, stock_extra_map, stock_raw_info_map, code2name):
    """
    处理所有有2只股票以上的行业内的股票排名
    """
    print(f"\n=== 行业内股票排名 ===")

    # 处理所有行业，不限制为前5名
    all_industries = sorted_industries

    for rank, (industry_name, industry_wins, industry_ties) in enumerate(all_industries, 1):
        print(f"\n--- 第{rank}名行业: {industry_name} ---")
        
        # 找到属于该行业的股票
        industry_stocks = []
        for stock_code, stock_industry in stock_to_industry.items():
            # 提取"一二级行业"中分隔线后的部分进行匹配
            actual_industry = stock_industry.split('-')[-1] if '-' in stock_industry else stock_industry
            if actual_industry == industry_name:
                industry_stocks.append(stock_code)
        
        if len(industry_stocks) < 2:
            print(f"  行业 {industry_name} 内股票数量少于2只，无法进行排名")
            continue
        
        print(f"  行业 {industry_name} 内股票: {len(industry_stocks)} 只")
        for stock_code in industry_stocks:
            stock_name = code2name.get(stock_code, '未知')
            print(f"    {stock_code}({stock_name})")
        
        # 进行行业内股票对比（不再生成日志文件）
        stock_wins, stock_ties = compare_stocks_within_industry(
            industry_name, industry_stocks, stock_item_scores_map,
            stock_extra_map, stock_raw_info_map, code2name, None
        )
        
        if not stock_wins:
            print(f"  行业 {industry_name} 内股票对比失败")
            continue
        
        # 对行业内股票进行排名
        sorted_stocks = sorted(
            [(code, wins, stock_ties[code]) for code, wins in stock_wins.items()],
            key=lambda x: (x[1], x[2]),  # 按胜场数和平局数排序
            reverse=True
        )
        
        print(f"  行业 {industry_name} 内股票排名:")
        for stock_rank, (stock_code, wins, ties) in enumerate(sorted_stocks, 1):
            stock_name = code2name.get(stock_code, '未知')
            print(f"    第{stock_rank}名: {stock_code}({stock_name})，胜出次数: {wins}，平局次数: {ties}")
        



# ========================= 本地程序对比分支辅助函数 =========================

def compute_industry_item_scores(industry_name, industry_tech_info, resonance_count):
    """基于行业技术指标和共振信息计算行业各维度评分
    
    Args:
        industry_name: 行业名称
        industry_tech_info: 行业技术指标信息
        resonance_count: 行业共振股票数量
        
    Returns:
        item_scores: 各维度评分字典
        extra: 扩展信息字典
    """
    item_scores = {}
    extra = {}

    # 0 超短、1 短线、2 中线 (基于行业技术指标)
    trend_params = [
        ("超短趋势判断", "超短线", 2, 1),
        ("短线趋势判断", "短线", 10, 5),
        ("中线趋势判断", "中线", 15, 8)
    ]
    for idx, (item_name, trend_type, hi, lo) in enumerate(trend_params):
        score, reason, details = stock_score.calculate_trend_score_detailed(industry_tech_info, trend_type, hi, lo)
        item_scores[item_name] = {"score": score, "reason": reason}
        
        # 优先使用结构化数据，若无则回退到文本解析
        if isinstance(details, dict):
            strong_cnt = details.get('start_cnt', 0) + details.get('confirm_cnt', 0) + details.get('strong_cnt', 0)
            weak_cnt = details.get('end_cnt', 0) + details.get('weak_cnt', 0)
            subtotal = details.get('subtotal', 0)
        else:
            subtotal = _parse_trend_subtotal(reason)
            strong_cnt, weak_cnt = _parse_strong_weak_cnt(reason)
            
        diff_cnt = strong_cnt - weak_cnt
        
        extra[item_name] = {
            "subtotal": subtotal,
            "strong_cnt": strong_cnt, 
            "weak_cnt": weak_cnt, 
            "diff": diff_cnt
        }

    # 资金面判断 - 基于行业技术指标中的成交量信息评估
    # 从行业技术指标中提取成交量相关信息来评估资金面
    import re
    volume_signals = 0
    positive_days = 0
    
    if industry_tech_info:
        # 统计成交量相关的正面信号
        volume_patterns = [
            r"放量",
            r"量价齐升",
            r"VOL大于MAVOL",
            r"MAVOL.*?上涨",
            r"成交量.*?放大",
            r"量能.*?充足"
        ]
        
        for pattern in volume_patterns:
            matches = re.findall(pattern, industry_tech_info, re.IGNORECASE)
            volume_signals += len(matches)
        
        # 统计包含正面成交量信息的交易日数
        # 查找K线数据行，统计包含正面成交量特征的天数
        kline_matches = re.findall(r'\d{4}-\d{2}-\d{2}:.*?(?=\n|\Z)', industry_tech_info, re.DOTALL)
        for kline in kline_matches:
            if any(re.search(pattern, kline, re.IGNORECASE) for pattern in volume_patterns):
                positive_days += 1
    
    # 基于成交量信号数量评分
    if volume_signals >= 8:
        score_fund = 3
        reason_fund = f"检测到{volume_signals}个成交量正面信号，资金面活跃，得3分"
    elif volume_signals >= 4:
        score_fund = 1  
        reason_fund = f"检测到{volume_signals}个成交量正面信号，资金面一般，得1分"
    else:
        score_fund = 0
        reason_fund = f"检测到{volume_signals}个成交量正面信号，资金面较弱，得0分"
    
    item_scores["资金面判断"] = {"score": score_fund, "reason": reason_fund}
    extra["资金面判断"] = {"positive_days": positive_days}

    # 消息面判断 - 行业固定为0分（按要求不使用大模型）
    item_scores["消息面判断"] = {"score": 0, "reason": "行业对比不考虑消息面因素"}
    extra["消息面判断"] = {
        "direction": "中性",
        "strength": "中性",
        "content": "行业对比不考虑消息面"
    }

    # 行业共振判断（直接基于共振股票数量）
    if resonance_count >= 5:
        score_resonance = 3  # 共振股票数>=5，得3分
        reason_resonance = f"行业共振股票数量({resonance_count})>=5，得3分"
    elif resonance_count >= 2:
        score_resonance = 1  # 共振股票数>=2，得1分
        reason_resonance = f"行业共振股票数量({resonance_count})>=2，得1分"
    else:
        score_resonance = 0  # 共振股票数<2，得0分
        reason_resonance = f"行业共振股票数量({resonance_count})<2，得0分"

    item_scores["行业共振判断"] = {"score": score_resonance, "reason": reason_resonance}

    extra["行业共振判断"] = {
        "res_cnt": resonance_count
    }

    # 基本面判断 - 行业对比不适用基本面分析（基本面是针对个股的）
    item_scores["基本面判断"] = {"score": 0, "reason": "行业对比不适用基本面分析"}
    extra["基本面判断"] = {
        "conditions_met": 0,
        "condition_a": False,
        "condition_b": False,
        "condition_c": False
    }

    # 盈亏比判断（基于行业技术指标）
    score_pl, reason_pl, details_pl = stock_score.calculate_profit_loss_ratio_score_detailed(industry_tech_info)
    item_scores["盈亏比判断"] = {"score": score_pl, "reason": reason_pl}

    # 从完整的行业技术指标信息中提取压力位空间（第8部分）
    # 总是从原始的industry_tech_info中提取，确保获得正确的压力位空间数据
    pressure_pct = _parse_pressure_space_pct(industry_tech_info)
    extra["盈亏比判断"] = {"pressure_pct": pressure_pct}

    return item_scores, extra


def compare_industries_local(pair_index, industry1_name, industry2_name, industry_item_scores_map, industry_extra_map, 
                           industry_raw_info_map, output_queue, output_file_lock, log_file):
    """本地评分方式比较两个行业，返回胜者等信息，同时记录到 compare_industry_local.txt"""
    
    items_order = [
        "超短趋势判断",
        "短线趋势判断",
        "中线趋势判断",
        "资金面判断",
        "消息面判断",
        "行业共振判断",
        "基本面判断",
        "盈亏比判断"
    ]

    # 记录每个项目 winner
    item_winners = {}
    log_lines = []

    for idx, item in enumerate(items_order):
        score1 = industry_item_scores_map[industry1_name][item]["score"]
        score2 = industry_item_scores_map[industry2_name][item]["score"]
        reason1 = industry_item_scores_map[industry1_name][item]["reason"]
        reason2 = industry_item_scores_map[industry2_name][item]["reason"]

        winner = None  # 'tie' or industry1_name/industry2_name
        detail_log = f"{idx}. {item}："

        # 先比得分
        if score1 > score2:
            winner = industry1_name
            detail_log += f"{industry1_name} 得分 {score1}>{score2}，直接胜出。"
        elif score2 > score1:
            winner = industry2_name
            detail_log += f"{industry2_name} 得分 {score2}>{score1}，直接胜出。"
        else:
            # 平分，按规则细化
            detail_log += f"得分相同({score1})。"
            if "趋势" in item:
                sub1 = industry_extra_map[industry1_name][item]["subtotal"]
                sub2 = industry_extra_map[industry2_name][item]["subtotal"]
                detail_log += f" 二级比较(小分): {industry1_name}({sub1}) vs {industry2_name}({sub2})。"
                if sub1 > sub2:
                    winner = industry1_name
                    detail_log += f" {industry1_name} 胜出。"
                elif sub2 > sub1:
                    winner = industry2_name
                    detail_log += f" {industry2_name} 胜出。"
                else:
                    detail_log += f" 小分相同({sub1})。"
                    # 比较信号个数
                    diff1 = industry_extra_map[industry1_name][item]["diff"]
                    diff2 = industry_extra_map[industry2_name][item]["diff"]
                    detail_log += f" 三级比较(强弱信号差): {industry1_name}({diff1}) vs {industry2_name}({diff2})。"
                    if diff1 > diff2:
                        winner = industry1_name
                        detail_log += f" {industry1_name} 胜出。"
                    elif diff2 > diff1:
                        winner = industry2_name
                        detail_log += f" {industry2_name} 胜出。"
                    else:
                        winner = "tie"
                        detail_log += f" 信号差相同({diff1})，判定平局。"
            elif item == "资金面判断":
                pos1 = industry_extra_map[industry1_name][item]["positive_days"]
                pos2 = industry_extra_map[industry2_name][item]["positive_days"]
                detail_log += f" 二级比较(合计正向天数): {industry1_name}({pos1}天) vs {industry2_name}({pos2}天)。"
                if pos1 > pos2:
                    winner = industry1_name
                    detail_log += f" {industry1_name} 胜出。"
                elif pos2 > pos1:
                    winner = industry2_name
                    detail_log += f" {industry2_name} 胜出。"
                else:
                    winner = "tie"
                    detail_log += " 正向天数相同，判定平局。"
            elif item == "消息面判断":
                # 行业对比中消息面都为中性，直接平局
                winner = "tie"
                detail_log += " 行业对比不考虑消息面，判定平局。"
            elif item == "行业共振判断":
                # 直接比较行业共振股票数
                rc1 = industry_extra_map[industry1_name][item]["res_cnt"]
                rc2 = industry_extra_map[industry2_name][item]["res_cnt"]
                detail_log += f" 二级比较(共振股票数): {industry1_name}({rc1}个) vs {industry2_name}({rc2}个)。"
                if rc1 > rc2:
                    winner = industry1_name
                    detail_log += f" {industry1_name} 胜出。"
                elif rc2 > rc1:
                    winner = industry2_name
                    detail_log += f" {industry2_name} 胜出。"
                else:
                    winner = "tie"
                    detail_log += " 共振数相同，判定平局。"
            elif item == "基本面判断":
                # 行业对比中基本面都为0分，直接平局
                winner = "tie"
                detail_log += " 行业对比不适用基本面分析，判定平局。"
            elif item == "盈亏比判断":
                # 暂时将盈亏比对比均设为平局
                winner = "tie"
                detail_log += " 根据用户要求，盈亏比对比暂时均设为平局。"
            else:
                winner = "tie"
                detail_log += " 无进一步判定规则，判定平局。"

        # 记录reason概要
        detail_log += f"\n   {industry1_name} 决策摘要：{reason1}\n   {industry2_name} 决策摘要：{reason2}"
        log_lines.append(detail_log)
        item_winners[item] = winner

    # 统计比分
    wins1 = sum(1 for w in item_winners.values() if w == industry1_name)
    wins2 = sum(1 for w in item_winners.values() if w == industry2_name)
    ties = sum(1 for w in item_winners.values() if w == "tie")

    if wins1 > wins2:
        overall_winner = industry1_name
    elif wins2 > wins1:
        overall_winner = industry2_name
    else:
        overall_winner = "tie"

    # 写入日志文件
    with output_file_lock:
        with open(log_file, "a", encoding="utf-8") as f:
            f.write(f"第{pair_index+1}组行业对比: {industry1_name} vs {industry2_name}\n")
            
            # 添加行业1的详细信息
            raw_info1 = industry_raw_info_map.get(industry1_name, {})
            f.write(f"\n--- {industry1_name} 行业详细信息 ---\n")
            f.write("【行业技术指标】\n")
            f.write(raw_info1.get("tech_info", "无行业技术指标信息") + "\n")
            f.write(f"\n【行业共振】\n共振股票数: {raw_info1.get('resonance_count', 0)}\n")

            # 添加行业2的详细信息
            raw_info2 = industry_raw_info_map.get(industry2_name, {})
            f.write(f"\n--- {industry2_name} 行业详细信息 ---\n")
            f.write("【行业技术指标】\n")
            f.write(raw_info2.get("tech_info", "无行业技术指标信息") + "\n")
            f.write(f"\n【行业共振】\n共振股票数: {raw_info2.get('resonance_count', 0)}\n")

            f.write("\n--- 对比过程 ---\n")
            for l in log_lines:
                f.write(l + "\n")
            f.write(f"比分：{wins1}:{wins2} (平局 {ties})\n")
            if overall_winner == "tie":
                f.write("最终结果：平局\n\n")
            else:
                f.write(f"最终结果：{overall_winner} 胜出\n\n")

    # 将结果放入队列
    output_queue.put((pair_index, overall_winner, industry1_name, "", industry2_name, "", f"{wins1}:{wins2}", "INDUSTRY_LOCAL", ties))
    return overall_winner, f"{wins1}:{wins2}", pair_index


def _parse_trend_subtotal(reason_text: str) -> int:
    """[Fallback] 从趋势维度的决策过程文本中提取两项合计的小分。"""
    import re
    if not isinstance(reason_text, str): return 0
    m = re.search(r"两项合计(\d+)分", reason_text)
    if m:
        try:
            return int(m.group(1))
        except (ValueError, TypeError):
            return 0
    return 0

def _parse_strong_weak_cnt(reason_text: str) -> tuple[int, int]:
    """[Fallback] 从决策过程文本中提取(强势信号数, 弱势信号数)。"""
    import re
    if not isinstance(reason_text, str): return (0, 0)
    strong = 0
    weak = 0
    strong_patterns = [
        r"强势信号\((\d+)个\)",
        r"趋势开始信号\((\d+)个\)",
        r"趋势确认信号\((\d+)个\)"
    ]
    for pat in strong_patterns:
        m = re.search(pat, reason_text)
        if m:
            try:
                strong += int(m.group(1))
            except (ValueError, TypeError):
                pass
    weak_patterns = [
        r"弱势信号\((\d+)个\)",
        r"趋势结束信号\((\d+)个\)"
    ]
    for pat in weak_patterns:
        m = re.search(pat, reason_text)
        if m:
            try:
                weak += int(m.group(1))
            except (ValueError, TypeError):
                pass
    return strong, weak

def _parse_positive_days(reason_text: str) -> int:
    """[Fallback] 从资金面决策文本中提取合计正向天数。"""
    import re
    if not isinstance(reason_text, str): return 0
    m = re.search(r"合计(\d+)天", reason_text)
    if m:
        try:
            return int(m.group(1))
        except (ValueError, TypeError):
            return 0
    return 0

def _parse_pressure_space_pct(reason_text: str) -> float:
    """从技术指标文本中提取距压力位空间百分比，匹配程序化输出格式。"""
    import re
    if not isinstance(reason_text, str): return 0.0

    # 精确匹配程序化输出格式：   - 压力位1: 856.82 (0.49%向上空间), 强度: 2
    pattern = r"\s*-\s*压力位\d+:\s*[\d\.]+\s*\(([\d\.]+)%向上空间\)"
    matches = re.findall(pattern, reason_text)

    if matches:
        try:
            # 返回第一个压力位的向上空间百分比
            return float(matches[0])
        except (ValueError, TypeError):
            pass

    return 0.0

def _parse_conditions_met(reason_text: str) -> int:
    """从基本面决策文本中提取满足条件数量。"""
    import re
    if not isinstance(reason_text, str): return 0
    m = re.search(r"满足条件数量:\s*(\d+)/3", reason_text)
    if m:
        try:
            return int(m.group(1))
        except (ValueError, TypeError):
            return 0
    return 0

def _extract_pressure_space_from_tech_info(tech_info: str) -> float:
    """从完整的技术指标信息中提取真实的压力位空间百分比"""
    import re
    if not isinstance(tech_info, str): 
        return 0.0
    
    # 匹配程序化输出格式：   - 压力位1: 856.82 (0.49%向上空间), 强度: 2
    pattern = r"\s*-\s*压力位\d+:\s*[\d\.]+\s*\(([\d\.]+)%向上空间\)"
    matches = re.findall(pattern, tech_info)
    
    if matches:
        try:
            # 返回第一个压力位的向上空间百分比
            return float(matches[0])
        except (ValueError, TypeError):
            pass
    
    # 如果没有找到精确格式，尝试其他可能的格式
    # 匹配格式：压力位空间：X.X%
    pattern2 = r"压力位空间[:：]\s*([\d\.]+)%"
    matches2 = re.findall(pattern2, tech_info)
    if matches2:
        try:
            return float(matches2[0])
        except (ValueError, TypeError):
            pass
    
    # 匹配格式：距压力位空间X.X%
    pattern3 = r"距压力位空间\s*([\d\.]+)%"
    matches3 = re.findall(pattern3, tech_info)
    if matches3:
        try:
            return float(matches3[0])
        except (ValueError, TypeError):
            pass
    
    return 0.0

def compute_local_item_scores(stock_dict, tech_info, history_info, industry_info, resonance_count, news_info=None, skip_news=False):
    """基于 Zstock_score 的各维度打分逻辑，返回 item_scores 及扩展信息。

    此版本已重构，不再解析reason文本，而是依赖打分函数返回的结构化details字典。

    Args:
        news_info: 消息面信息字典，包含direction和strength字段
        skip_news: 是否跳过消息面计算（模式1-4使用）
    """
    item_scores = {}
    extra = {}

    # 0 超短、1 短线、2 中线
    trend_params = [
        ("超短趋势判断", "超短线", 2, 1),
        ("短线趋势判断", "短线", 10, 5),
        ("中线趋势判断", "中线", 15, 8)
    ]
    for idx, (item_name, trend_type, hi, lo) in enumerate(trend_params):
        # 假设函数返回 (得分, 理由文本, 包含详细计数的字典)
        score, reason, details = stock_score.calculate_trend_score_detailed(tech_info, trend_type, hi, lo)
        item_scores[item_name] = {"score": score, "reason": reason}

        # 优先使用结构化数据，若无则回退到文本解析
        if isinstance(details, dict):
            strong_cnt = details.get('start_cnt', 0) + details.get('confirm_cnt', 0) + details.get('strong_cnt', 0)
            weak_cnt = details.get('end_cnt', 0) + details.get('weak_cnt', 0)
            subtotal = details.get('subtotal', 0)
        else:
            subtotal = _parse_trend_subtotal(reason)
            strong_cnt, weak_cnt = _parse_strong_weak_cnt(reason)

        diff_cnt = strong_cnt - weak_cnt

        extra[item_name] = {
            "subtotal": subtotal,
            "strong_cnt": strong_cnt,
            "weak_cnt": weak_cnt,
            "diff": diff_cnt
        }

    # 资金面
    stock_code = stock_dict.get('代码', '') or '未知'
    stock_name = stock_dict.get('名称', '') or '未知'
    score_fund, reason_fund, details_fund = stock_score.calculate_fund_score_detailed(history_info, stock_code, stock_name)
    item_scores["资金面判断"] = {"score": score_fund, "reason": reason_fund}

    if isinstance(details_fund, dict):
        positive_days = details_fund.get('positive_days', 0)
        # 提取子维度分数信息
        activity_score = details_fund.get('activity_score', 0)
        flow_score = details_fund.get('flow_score', 0)
        total_score = activity_score + flow_score
    else:
        positive_days = _parse_positive_days(reason_fund)
        # 如果没有详细信息，尝试从理由文本中解析子维度分数
        activity_score = 1 if "资金活跃度得分: 1分" in reason_fund else 0
        flow_score = 1 if "主力净流入天数得分: 1分" in reason_fund else 0
        total_score = activity_score + flow_score

    extra["资金面判断"] = {
        "positive_days": positive_days,
        "activity_score": activity_score,
        "flow_score": flow_score,
        "total_score": total_score
    }

    # 消息面 (根据是否跳过消息面计算决定)
    if skip_news:
        # 模式1-4：跳过消息面计算，直接设为默认值
        score_news = 0
        reason_news = "模式1-4跳过消息面计算"
        extra_news = {
            "direction": "中性",
            "strength": "中性",
            "content": "模式1-4跳过消息面计算"
        }
    elif news_info and isinstance(news_info, dict) and "direction" in news_info:
        # 有消息面信息时，根据方向和强度计算分数
        direction = news_info.get("direction", "中性")
        strength = news_info.get("strength", "中性")
        content = news_info.get("content", "")

        # 计算消息面分数
        if direction == "利多":
            if strength == "强":
                score_news = 8
            elif strength == "弱":
                score_news = 5
            else:  # 中性（这种情况理论上不会出现）
                score_news = 3
        elif direction == "利空":
            if strength == "强":
                score_news = -8
            elif strength == "弱":
                score_news = -5
            else:  # 中性（这种情况理论上不会出现）
                score_news = -3
        else:  # 中性
            score_news = 0

        reason_news = f"消息面方向：{direction}，强度：{strength}，得分：{score_news}"

        # 保存额外信息用于对比
        extra_news = {
            "direction": direction,
            "strength": strength,
            "content": content
        }
    else:
        # 没有消息面信息时，使用原来的逻辑（本地模式统一为0分）
        score_news, reason_news, _ = stock_score.calculate_news_score_detailed(tech_info, is_local_calculation=True)
        extra_news = {
            "direction": "中性",
            "strength": "中性",
            "content": "未获取消息面信息"
        }

    item_scores["消息面判断"] = {"score": score_news, "reason": reason_news}
    extra["消息面判断"] = extra_news

    # 行业趋势及共振判断（合并原行业趋势判断和行业共振判断）
    score_ind_res, reason_ind_res, details_ind_res = stock_score.calculate_industry_trend_resonance_score_detailed(industry_info, resonance_count)
    item_scores["行业趋势及共振判断"] = {"score": score_ind_res, "reason": reason_ind_res}
    
    # 优先使用结构化数据，若无则回退到文本解析
    if isinstance(details_ind_res, dict):
        strong_ind = details_ind_res.get('start_cnt', 0) + details_ind_res.get('confirm_cnt', 0) + details_ind_res.get('strong_cnt', 0)
        weak_ind = details_ind_res.get('end_cnt', 0) + details_ind_res.get('weak_cnt', 0)
        subtotal_ind = details_ind_res.get('subtotal', 0)
        res_cnt = details_ind_res.get('resonance_count', resonance_count)
    else:
        subtotal_ind = _parse_trend_subtotal(reason_ind_res)
        strong_ind, weak_ind = _parse_strong_weak_cnt(reason_ind_res)
        res_cnt = resonance_count
        
    diff_ind = strong_ind - weak_ind

    extra["行业趋势及共振判断"] = {
        "subtotal": subtotal_ind,
        "strong_cnt": strong_ind,
        "weak_cnt": weak_ind,
        "diff": diff_ind,
        "res_cnt": res_cnt
    }

    # 基本面判断
    stock_code = stock_dict.get('代码', '')
    if stock_code:
        score_fundamental, reason_fundamental, details_fundamental = stock_score.calculate_fundamental_score_detailed(stock_code)
        item_scores["基本面判断"] = {"score": score_fundamental, "reason": reason_fundamental}

        # 提取基本面详细信息用于对比
        if isinstance(details_fundamental, dict):
            conditions_met = details_fundamental.get('conditions_met', 0)
            condition_a = details_fundamental.get('condition_a', False)
            condition_b = details_fundamental.get('condition_b', False)
            condition_c = details_fundamental.get('condition_c', False)
        else:
            # 回退到文本解析
            conditions_met = _parse_conditions_met(reason_fundamental)
            condition_a = "条件a满足" in reason_fundamental
            condition_b = "条件b满足" in reason_fundamental
            condition_c = "条件c满足" in reason_fundamental

        extra["基本面判断"] = {
            "conditions_met": conditions_met,
            "condition_a": condition_a,
            "condition_b": condition_b,
            "condition_c": condition_c
        }
    else:
        # 无股票代码时的默认处理
        item_scores["基本面判断"] = {"score": 0, "reason": "无股票代码，无法进行基本面分析"}
        extra["基本面判断"] = {
            "conditions_met": 0,
            "condition_a": False,
            "condition_b": False,
            "condition_c": False
        }

    # 盈亏比 - 新逻辑：不在此处预计算，而是在对比时计算
    # 这里只保存必要的信息，实际对比在 compare_profit_loss_ratio_new() 中进行
    item_scores["盈亏比判断"] = {
        "score": 0,  # 占位符，实际得分在对比时确定
        "reason": f"盈亏比分析 - 股票代码: {stock_code}\n使用新的find3buy对比逻辑，需要在对比时计算。"
    }

    # 从技术指标中提取压力位空间信息（因为不再预计算盈亏比）
    pressure_pct = _extract_pressure_space_from_tech_info(tech_info)

    extra["盈亏比判断"] = {
        "pressure_pct": pressure_pct,
        "ratio": 0.0,  # 占位符
        "ratio_details": {"method": "find3buy_comparison", "note": "实际计算在对比时进行"}
    }

    return item_scores, extra


def compare_stocks_local(pair_index, stock1, stock2, stock_item_scores_map, stock_extra_map, stock_raw_info_map, output_queue, output_file_lock, log_file):
    """本地评分方式比较两支股票，返回胜者等信息，同时记录到 compare_local.txt"""
    code1 = stock1.get('代码', '未知') or '未知'
    code2 = stock2.get('代码', '未知') or '未知'
    name1 = stock1.get('名称', '未知') or '未知'
    name2 = stock2.get('名称', '未知') or '未知'

    items_order = [
        "超短趋势判断",
        "短线趋势判断",
        "中线趋势判断",
        "资金面判断",
        "消息面判断",
        "行业趋势及共振判断",
        "基本面判断",
        "盈亏比判断"
    ]

    # 记录每个项目 winner
    item_winners = {}
    log_lines = []

    for idx, item in enumerate(items_order):
        score1 = stock_item_scores_map[code1][item]["score"]
        score2 = stock_item_scores_map[code2][item]["score"]
        reason1 = stock_item_scores_map[code1][item]["reason"]
        reason2 = stock_item_scores_map[code2][item]["reason"]

        winner = None  # 'tie' or code1/code2
        detail_log = f"{idx}. {item}："

        # 先比得分
        if score1 > score2:
            winner = code1
            detail_log += f"{code1} 得分 {score1}>{score2}，直接胜出。"
        elif score2 > score1:
            winner = code2
            detail_log += f"{code2} 得分 {score2}>{score1}，直接胜出。"
        else:
            # 平分，按规则细化
            detail_log += f"得分相同({score1})。"
            if "趋势" in item:
                sub1 = stock_extra_map[code1][item]["subtotal"]
                sub2 = stock_extra_map[code2][item]["subtotal"]
                detail_log += f" 二级比较(小分): {code1}({sub1}) vs {code2}({sub2})。"
                if sub1 > sub2:
                    winner = code1
                    detail_log += f" {code1} 胜出。"
                elif sub2 > sub1:
                    winner = code2
                    detail_log += f" {code2} 胜出。"
                else:
                    detail_log += f" 小分相同({sub1})。"
                    # 比较信号个数
                    diff1 = stock_extra_map[code1][item]["diff"]
                    diff2 = stock_extra_map[code2][item]["diff"]
                    detail_log += f" 三级比较(强弱信号差): {code1}({diff1}) vs {code2}({diff2})。"
                    if diff1 > diff2:
                        winner = code1
                        detail_log += f" {code1} 胜出。"
                    elif diff2 > diff1:
                        winner = code2
                        detail_log += f" {code2} 胜出。"
                    else:
                        winner = "tie"
                        detail_log += f" 信号差相同({diff1})，判定平局。"
            elif item == "资金面判断":
                # 使用新的子维度分数之和进行对比
                total1 = stock_extra_map[code1][item].get("total_score", 0)
                total2 = stock_extra_map[code2][item].get("total_score", 0)
                activity1 = stock_extra_map[code1][item].get("activity_score", 0)
                activity2 = stock_extra_map[code2][item].get("activity_score", 0)
                flow1 = stock_extra_map[code1][item].get("flow_score", 0)
                flow2 = stock_extra_map[code2][item].get("flow_score", 0)

                detail_log += f" 二级比较(子维度分数之和): {code1}({total1}分=活跃度{activity1}+流入{flow1}) vs {code2}({total2}分=活跃度{activity2}+流入{flow2})。"

                if total1 > total2:
                    winner = code1
                    detail_log += f" {code1} 胜出。"
                elif total2 > total1:
                    winner = code2
                    detail_log += f" {code2} 胜出。"
                else:
                    winner = "tie"
                    detail_log += " 子维度分数之和相同，判定平局。"
            elif item == "消息面判断":
                # 新增：消息面判断的二级比较逻辑
                dir1 = stock_extra_map[code1][item]["direction"]
                str1 = stock_extra_map[code1][item]["strength"]
                dir2 = stock_extra_map[code2][item]["direction"]
                str2 = stock_extra_map[code2][item]["strength"]
                detail_log += f" 二级比较(消息面): {code1}({dir1}/{str1}) vs {code2}({dir2}/{str2})。"
                
                # 方向优先级：利多 > 中性 > 利空
                direction_priority = {"利多": 2, "中性": 1, "利空": 0}
                dir1_pri = direction_priority.get(dir1, 1)
                dir2_pri = direction_priority.get(dir2, 1)
                
                if dir1_pri > dir2_pri:
                    winner = code1
                    detail_log += f" {code1} 方向更优胜出。"
                elif dir2_pri > dir1_pri:
                    winner = code2
                    detail_log += f" {code2} 方向更优胜出。"
                else:
                    # 方向相同，比较强度：强 > 弱 > 中性
                    if dir1 == "中性" and dir2 == "中性":
                        winner = "tie"
                        detail_log += " 方向都是中性，判定平局。"
                    else:
                        strength_priority = {"强": 2, "弱": 1, "中性": 0}
                        str1_pri = strength_priority.get(str1, 0)
                        str2_pri = strength_priority.get(str2, 0)
                        
                        if str1_pri > str2_pri:
                            winner = code1
                            detail_log += f" 方向相同，{code1} 强度更高胜出。"
                        elif str2_pri > str1_pri:
                            winner = code2
                            detail_log += f" 方向相同，{code2} 强度更高胜出。"
                        else:
                            winner = "tie"
                            detail_log += " 方向和强度都相同，判定平局。"
            elif item == "行业趋势及共振判断":
                # 先进行行业趋势相关的二级比较
                sub1 = stock_extra_map[code1][item]["subtotal"]
                sub2 = stock_extra_map[code2][item]["subtotal"]
                detail_log += f" 二级比较(行业趋势小分): {code1}({sub1}) vs {code2}({sub2})。"
                if sub1 > sub2:
                    winner = code1
                    detail_log += f" {code1} 胜出。"
                elif sub2 > sub1:
                    winner = code2
                    detail_log += f" {code2} 胜出。"
                else:
                    detail_log += f" 行业趋势小分相同({sub1})。"
                    # 比较行业趋势信号个数差
                    diff1 = stock_extra_map[code1][item]["diff"]
                    diff2 = stock_extra_map[code2][item]["diff"]
                    detail_log += f" 三级比较(行业趋势信号差): {code1}({diff1}) vs {code2}({diff2})。"
                    if diff1 > diff2:
                        winner = code1
                        detail_log += f" {code1} 胜出。"
                    elif diff2 > diff1:
                        winner = code2
                        detail_log += f" {code2} 胜出。"
                    else:
                        detail_log += f" 行业趋势信号差相同({diff1})。"
                        # 最后比较行业共振股票数
                        rc1 = stock_extra_map[code1][item]["res_cnt"]
                        rc2 = stock_extra_map[code2][item]["res_cnt"]
                        detail_log += f" 四级比较(共振股票数): {code1}({rc1}个) vs {code2}({rc2}个)。"
                        if rc1 > rc2:
                            winner = code1
                            detail_log += f" {code1} 胜出。"
                        elif rc2 > rc1:
                            winner = code2
                            detail_log += f" {code2} 胜出。"
                        else:
                            winner = "tie"
                            detail_log += " 共振数相同，判定平局。"
            elif item == "基本面判断":
                # 基本面对比：先比评分，再比符合条件项目数
                cond_met1 = stock_extra_map[code1][item]["conditions_met"]
                cond_met2 = stock_extra_map[code2][item]["conditions_met"]
                detail_log += f" 二级比较(符合条件数): {code1}({cond_met1}项) vs {code2}({cond_met2}项)。"
                if cond_met1 > cond_met2:
                    winner = code1
                    detail_log += f" {code1} 胜出。"
                elif cond_met2 > cond_met1:
                    winner = code2
                    detail_log += f" {code2} 胜出。"
                else:
                    winner = "tie"
                    detail_log += " 符合条件数相同，判定平局。"
            elif item == "盈亏比判断":
                # 使用新的盈亏比对比逻辑，基于 find3buy 的方法
                winner_code, comparison_info = compare_profit_loss_ratio_new(code1, name1, code2, name2)

                # 提取对比信息
                case = comparison_info.get('case', 'unknown')
                reason = comparison_info.get('reason', '无详细信息')

                if case == "position_advantage":
                    detail_log += f" 位置优势判断：{reason}"
                elif case == "both_above_zs":
                    energy1 = comparison_info.get('energy_density1', 0)
                    energy2 = comparison_info.get('energy_density2', 0)
                    detail_log += f" 两股票均在中枢上方，{code1} 能量密度: {energy1:.2f}, {code2} 能量密度: {energy2:.2f}。{reason}"
                elif case == "both_below_zs":
                    ratio1 = comparison_info.get('ratio1', 0)
                    ratio2 = comparison_info.get('ratio2', 0)
                    detail_log += f" 两股票均在中枢下方，{code1} 盈亏比: {ratio1:.4f}, {code2} 盈亏比: {ratio2:.4f}。{reason}"
                else:
                    detail_log += f" {reason}"

                if winner_code == code1:
                    winner = code1
                    detail_log += f" {code1} 胜出。"
                elif winner_code == code2:
                    winner = code2
                    detail_log += f" {code2} 胜出。"
                else:
                    winner = "tie"
                    detail_log += " 判定平局。"
            else:
                winner = "tie"
                detail_log += " 无进一步判定规则，判定平局。"

        # 记录reason概要
        detail_log += f"\n   {code1} 决策摘要：{reason1}\n   {code2} 决策摘要：{reason2}"
        log_lines.append(detail_log)
        item_winners[item] = winner

    # 统计比分
    wins1 = sum(1 for w in item_winners.values() if w == code1)
    wins2 = sum(1 for w in item_winners.values() if w == code2)
    ties = sum(1 for w in item_winners.values() if w == "tie")

    if wins1 > wins2:
        overall_winner = code1
    elif wins2 > wins1:
        overall_winner = code2
    else:
        overall_winner = "tie"

    # 写入日志文件（如果指定了日志文件）
    if log_file:
        with output_file_lock:
            with open(log_file, "a", encoding="utf-8") as f:
                f.write(f"第{pair_index+1}组: {code1}({name1}) vs {code2}({name2})\n")

                # 添加股票1的行情详细信息
                raw_info1 = stock_raw_info_map.get(code1, {})
                f.write(f"\n--- {code1}({name1}) 行情详细信息 ---\n")
                f.write("【技术指标】\n")
                f.write(raw_info1.get("tech_info", "无技术指标信息") + "\n")
                f.write("\n【资金面情况】\n")
                f.write(raw_info1.get("history_info", "无资金面信息") + "\n")
                f.write("\n【行业技术指标】\n")
                f.write(raw_info1.get("industry_info", "无行业技术指标信息") + "\n")
                f.write(f"\n【行业共振】\n共振股票数: {raw_info1.get('resonance_count', 0)}\n")

                # 新增：显示消息面信息
                news_info1 = raw_info1.get("news_info", {})
                if news_info1:
                    f.write(f"\n【消息面信息】\n")
                    f.write(f"方向: {news_info1.get('direction', '未知')}\n")
                    f.write(f"强度: {news_info1.get('strength', '未知')}\n")
                    f.write(f"详细内容: {news_info1.get('content', '无详细内容')}\n")
                else:
                    f.write(f"\n【消息面信息】\n无消息面信息\n")

                # 新增：显示基本面信息
                if code1 in stock_item_scores_map and "基本面判断" in stock_item_scores_map[code1]:
                    fundamental_score1 = stock_item_scores_map[code1]["基本面判断"]["score"]
                    fundamental_reason1 = stock_item_scores_map[code1]["基本面判断"]["reason"]
                    conditions_met1 = stock_extra_map[code1]["基本面判断"]["conditions_met"]
                    f.write(f"\n【基本面信息】\n")
                    f.write(f"基本面评分: {fundamental_score1}分\n")
                    f.write(f"符合条件数: {conditions_met1}/3项\n")
                    f.write(f"详细分析: {fundamental_reason1}\n")
                else:
                    f.write(f"\n【基本面信息】\n无基本面信息\n")

                # 添加股票2的行情详细信息
                raw_info2 = stock_raw_info_map.get(code2, {})
                f.write(f"\n--- {code2}({name2}) 行情详细信息 ---\n")
                f.write("【技术指标】\n")
                f.write(raw_info2.get("tech_info", "无技术指标信息") + "\n")
                f.write("\n【资金面情况】\n")
                f.write(raw_info2.get("history_info", "无资金面信息") + "\n")
                f.write("\n【行业技术指标】\n")
                f.write(raw_info2.get("industry_info", "无行业技术指标信息") + "\n")
                f.write(f"\n【行业共振】\n共振股票数: {raw_info2.get('resonance_count', 0)}\n")

                # 新增：显示消息面信息
                news_info2 = raw_info2.get("news_info", {})
                if news_info2:
                    f.write(f"\n【消息面信息】\n")
                    f.write(f"方向: {news_info2.get('direction', '未知')}\n")
                    f.write(f"强度: {news_info2.get('strength', '未知')}\n")
                    f.write(f"详细内容: {news_info2.get('content', '无详细内容')}\n")
                else:
                    f.write(f"\n【消息面信息】\n无消息面信息\n")

                # 新增：显示基本面信息
                if code2 in stock_item_scores_map and "基本面判断" in stock_item_scores_map[code2]:
                    fundamental_score2 = stock_item_scores_map[code2]["基本面判断"]["score"]
                    fundamental_reason2 = stock_item_scores_map[code2]["基本面判断"]["reason"]
                    conditions_met2 = stock_extra_map[code2]["基本面判断"]["conditions_met"]
                    f.write(f"\n【基本面信息】\n")
                    f.write(f"基本面评分: {fundamental_score2}分\n")
                    f.write(f"符合条件数: {conditions_met2}/3项\n")
                    f.write(f"详细分析: {fundamental_reason2}\n")
                else:
                    f.write(f"\n【基本面信息】\n无基本面信息\n")

                f.write("\n--- 对比过程 ---\n")
                for l in log_lines:
                    f.write(l + "\n")
                f.write(f"比分：{wins1}:{wins2} (平局 {ties})\n")
                if overall_winner == "tie":
                    f.write("最终结果：平局\n\n")
                else:
                    win_name = name1 if overall_winner == code1 else name2
                    f.write(f"最终结果：{overall_winner}({win_name}) 胜出\n\n")

    # 将结果放入队列，沿用旧格式方便后续统计
    output_queue.put((pair_index, overall_winner, code1, name1, code2, name2, f"{wins1}:{wins2}", "LOCAL", ties))
    return overall_winner, f"{wins1}:{wins2}", pair_index

# ======================== 本地程序对比分支辅助函数结束 =======================

def get_stock_zs_info(stock_code: str):
    """
    获取股票的中枢信息（上沿价格、当前价格、中枢持续时长）

    参数:
        stock_code: 股票代码

    返回:
        dict: {
            'upper_price': float,      # 中枢上沿价格
            'current_price': float,    # 当前价格
            'duration_days': int,      # 中枢持续天数
            'above_upper': bool,       # 是否位于上沿上方
            'error': str               # 错误信息（如有）
        }
    """
    try:
        # 参数验证
        if not stock_code or not isinstance(stock_code, str) or len(stock_code.strip()) == 0:
            return {
                'upper_price': None,
                'current_price': None,
                'duration_days': None,
                'above_upper': False,
                'error': '股票代码不能为空或无效'
            }

        if ZS_INDICATORS_AVAILABLE:
            # 使用新的中枢指标接口
            print(f"使用新接口获取股票 {stock_code} 的中枢信息...")

            result = get_zs_indicators(stock_code, KL_TYPE.K_DAY)

            if "error" not in result:
                # 提取所需信息
                upper_price = result.get('zs_upper')
                current_price = result.get('current_price')
                start_time = result.get('zs_start_time')
                end_time = result.get('zs_end_time')

                if upper_price is not None and current_price is not None:
                    # 计算中枢持续时长
                    duration_days = 0
                    if start_time and end_time:
                        try:
                            from datetime import datetime
                            if isinstance(start_time, str) and isinstance(end_time, str):
                                # 处理不同的时间格式
                                for fmt in ['%Y-%m-%d', '%Y/%m/%d', '%Y-%m-%d %H:%M:%S', '%Y/%m/%d %H:%M:%S']:
                                    try:
                                        start_date = datetime.strptime(start_time.split(' ')[0], fmt.split(' ')[0])
                                        end_date = datetime.strptime(end_time.split(' ')[0], fmt.split(' ')[0])
                                        duration_days = (end_date - start_date).days + 1
                                        break
                                    except ValueError:
                                        continue
                                print(f"计算得到中枢持续时长: {duration_days}天")
                        except Exception as e:
                            print(f"计算中枢持续时长失败: {e}")
                            duration_days = 0

                    # 判断是否位于上沿上方
                    above_upper = current_price > upper_price

                    print(f"新接口成功获取中枢信息 - 上沿: {upper_price:.2f}, 当前价: {current_price:.2f}, 持续: {duration_days}天")

                    return {
                        'upper_price': upper_price,
                        'current_price': current_price,
                        'duration_days': duration_days,
                        'above_upper': above_upper,
                        'error': None
                    }
                else:
                    print(f"新接口返回数据不完整，降级到原有方法")
            else:
                print(f"新接口获取失败: {result['error']}，降级到原有方法")

        # 降级到原有方法
        if not CHAN_ANALYSIS_AVAILABLE:
            return {'error': '缠论分析模块和中枢指标接口均不可用'}

        import sys
        import os
        import logging
        from datetime import datetime, timedelta

        # 导入缠论相关模块
        sys.path.append(os.path.dirname(__file__))
        try:
            from Zstock_find3buy_final import check_3buy_conditions_debug
        except ImportError:
            try:
                from Zstock_find3buy import check_3buy_conditions_debug
            except ImportError:
                return {'error': '无法导入缠论分析函数'}

        try:
            from libs.chan.Common.CEnum import KL_TYPE as CHAN_KL_TYPE
        except ImportError:
            try:
                from Common.CEnum import KL_TYPE as CHAN_KL_TYPE
            except ImportError:
                # 使用简单的枚举定义
                class CHAN_KL_TYPE:
                    K_DAY = "K_DAY"

        print(f"降级方法: 分析股票 {stock_code}")

        # 临时修改日志级别，捕获中枢信息
        logger = logging.getLogger()
        original_level = logger.level

        # 创建一个自定义的日志处理器来捕获中枢信息
        captured_logs = []

        class CaptureHandler(logging.Handler):
            def emit(self, record):
                captured_logs.append(record.getMessage())

        capture_handler = CaptureHandler()
        logger.addHandler(capture_handler)
        logger.setLevel(logging.INFO)

        try:
            # 首先尝试调用check_3buy_conditions_debug，如果返回结果直接使用
            result = check_3buy_conditions_debug(stock_code, stock_code, CHAN_KL_TYPE.K_DAY)

            # 如果3买分析返回了结果，直接使用
            if result is not None and isinstance(result, dict):
                upper_price = result.get('zs_upper')
                current_price = result.get('current_price')

                if upper_price is not None and current_price is not None:
                    print(f"降级方法: 从3买结果直接获取 - 上沿: {upper_price}, 当前价: {current_price}")
                    return {
                        'upper_price': upper_price,
                        'current_price': current_price,
                        'duration_days': 0,  # 3买结果中没有持续时长信息
                        'above_upper': current_price > upper_price,
                        'error': None
                    }

            # 如果3买分析没有返回有效结果，从捕获的日志中提取中枢信息
            upper_price = None
            current_price = None
            start_time_str = None
            end_time_str = None

            for log_msg in captured_logs:
                if "最终确定中枢的上沿值:" in log_msg:
                    try:
                        upper_price = float(log_msg.split("最终确定中枢的上沿值:")[1].strip())
                    except:
                        pass
                elif "当前价:" in log_msg:
                    try:
                        current_price = float(log_msg.split("当前价:")[1].strip())
                    except:
                        pass
                elif "最终确定中枢的起止时间:" in log_msg:
                    try:
                        time_part = log_msg.split("最终确定中枢的起止时间:")[1].strip()
                        if " 到 " in time_part:
                            start_time_str, end_time_str = time_part.split(" 到 ")
                    except:
                        pass

            print(f"降级方法: 从日志提取 - 上沿: {upper_price}, 当前价: {current_price}, 起止时间: {start_time_str} 到 {end_time_str}")

            if upper_price is None or current_price is None:
                return {'error': f'股票 {stock_code} 无法获取中枢信息，可能不符合3买条件或数据不足'}

            # 计算中枢持续时长
            duration_days = 0
            if start_time_str and end_time_str:
                try:
                    start_date = datetime.strptime(start_time_str.strip(), '%Y/%m/%d')
                    end_date = datetime.strptime(end_time_str.strip(), '%Y/%m/%d')
                    duration_days = (end_date - start_date).days + 1
                    print(f"降级方法: 计算得到中枢持续时长: {duration_days}天")
                except Exception as e:
                    print(f"计算中枢持续时长失败: {e}")
                    duration_days = 0

            # 判断是否位于上沿上方
            above_upper = current_price > upper_price

            return {
                'upper_price': upper_price,
                'current_price': current_price,
                'duration_days': duration_days,
                'above_upper': above_upper,
                'error': None
            }

        finally:
            # 恢复原始日志设置
            logger.removeHandler(capture_handler)
            logger.setLevel(original_level)

    except Exception as e:
        return {'error': f'获取中枢信息出错: {e}'}

def _parse_positive_signal_cnt(reason_text):
    """从决策过程文本中提取'趋势开始/确认/强势'三类信号的总数量"""
    import re
    total = 0
    patterns = [r"趋势开始信号\((\d+)个\)", r"趋势确认信号\((\d+)个\)", r"强势信号\((\d+)个\)"]
    for pat in patterns:
        m = re.search(pat, reason_text)
        if m:
            try:
                total += int(m.group(1))
            except ValueError:
                pass
    return total

def _parse_strong_weak_cnt(reason_text):
    """返回(强势信号数, 弱势信号数)；
    强势 = 强势信号 + 趋势开始信号 + 趋势确认信号
    弱势 = 弱势信号 + 趋势结束信号
    """
    import re
    strong = 0
    weak = 0
    # 强势相关
    strong_patterns = [
        r"强势信号\((\d+)个\)",
        r"趋势开始信号\((\d+)个\)",
        r"趋势确认信号\((\d+)个\)"
    ]
    for pat in strong_patterns:
        m = re.search(pat, reason_text)
        if m:
            try:
                strong += int(m.group(1))
            except ValueError:
                pass
    # 弱势相关
    weak_patterns = [
        r"弱势信号\((\d+)个\)",
        r"趋势结束信号\((\d+)个\)"
    ]
    for pat in weak_patterns:
        m = re.search(pat, reason_text)
        if m:
            try:
                weak += int(m.group(1))
            except ValueError:
                pass
    return strong, weak

def compare_profit_loss_ratio_new(stock1_code, stock1_name, stock2_code, stock2_name):
    """
    新的盈亏比对比函数，完全重构为用户要求的对比逻辑

    对比逻辑：
    1. 首先引入 Zstock_find3buy.py 的能力
    2. 用find3buy的方法来判断现价是否在最近的中枢上沿之上
    3. 如两只股票有一只在上沿之上，有一只不在，在中枢上沿之上的那个股票直接获胜
    4. 如两只股票都在中枢上沿之上，跳过一切其它逻辑，直接对比来自 find3buy 定义的"中枢能量密度"值，该数值更大的一方获胜
    5. 如两只股票都在中枢上沿之下，则按照 find3buy程序里的"盈亏比"数值的逻辑（取算法1和算法2中更大的那个）来对比大小

    返回:
        (胜者代码, 详细对比信息字典)
    """
    try:
        if not CHAN_ANALYSIS_AVAILABLE:
            return "tie", {"error": "缠论分析模块不可用", "method": "unavailable"}

        # 参数验证
        if not stock1_code or not stock2_code:
            return "tie", {"error": "股票代码不能为空", "method": "parameter_error"}

        # 优先使用新的中枢指标接口
        if ZS_INDICATORS_AVAILABLE:
            print(f"使用新接口进行盈亏比对比: {stock1_code} vs {stock2_code}")

            # 获取两只股票的中枢指标
            result1 = get_zs_indicators(stock1_code, KL_TYPE.K_DAY)
            result2 = get_zs_indicators(stock2_code, KL_TYPE.K_DAY)

            if "error" not in result1 and "error" not in result2:
                # 提取关键信息
                zs_upper1 = result1.get('zs_upper', 0)
                zs_upper2 = result2.get('zs_upper', 0)
                current_price1 = result1.get('current_price', 0)
                current_price2 = result2.get('current_price', 0)

                print(f"新接口获取成功 - {stock1_code}: 上沿{zs_upper1:.2f}, 当前价{current_price1:.2f}")
                print(f"新接口获取成功 - {stock2_code}: 上沿{zs_upper2:.2f}, 当前价{current_price2:.2f}")
            else:
                print(f"新接口获取失败，降级到原有方法")
                # 降级到原有方法
                return _compare_profit_loss_ratio_with_find3buy(stock1_code, stock1_name, stock2_code, stock2_name)
        else:
            # 降级到原有方法
            return _compare_profit_loss_ratio_with_find3buy(stock1_code, stock1_name, stock2_code, stock2_name)

        # 判断现价是否在最近的中枢上沿之上
        above_zs1 = current_price1 > zs_upper1 if zs_upper1 > 0 else False
        above_zs2 = current_price2 > zs_upper2 if zs_upper2 > 0 else False

        comparison_info = {
            "stock1_info": {
                "code": stock1_code,
                "name": stock1_name,
                "current_price": current_price1,
                "zs_upper": zs_upper1,
                "above_zs": above_zs1
            },
            "stock2_info": {
                "code": stock2_code,
                "name": stock2_name,
                "current_price": current_price2,
                "zs_upper": zs_upper2,
                "above_zs": above_zs2
            },
            "method": "zs_indicators_analysis"
        }

        # 情况1：如两只股票有一只在上沿之上，有一只不在，在中枢上沿之上的那个股票直接获胜
        if above_zs1 and not above_zs2:
            comparison_info["case"] = "position_advantage"
            comparison_info["winner"] = stock1_code
            comparison_info["reason"] = f"{stock1_name}({stock1_code})现价{current_price1:.2f}位于中枢上沿{zs_upper1:.2f}之上，而{stock2_name}({stock2_code})现价{current_price2:.2f}位于中枢上沿{zs_upper2:.2f}之下，前者获胜"
            return stock1_code, comparison_info
        elif above_zs2 and not above_zs1:
            comparison_info["case"] = "position_advantage"
            comparison_info["winner"] = stock2_code
            comparison_info["reason"] = f"{stock2_name}({stock2_code})现价{current_price2:.2f}位于中枢上沿{zs_upper2:.2f}之上，而{stock1_name}({stock1_code})现价{current_price1:.2f}位于中枢上沿{zs_upper1:.2f}之下，前者获胜"
            return stock2_code, comparison_info

        # 情况2：如两只股票都在中枢上沿之上，直接对比来自新接口的"中枢能量密度"值
        elif above_zs1 and above_zs2:
            # 使用新接口的中枢能量密度
            energy_density1 = result1.get('zs_energy_density', 0)
            energy_density2 = result2.get('zs_energy_density', 0)

            comparison_info["case"] = "both_above_zs"
            comparison_info["energy_density1"] = energy_density1
            comparison_info["energy_density2"] = energy_density2

            if energy_density1 > energy_density2:
                comparison_info["winner"] = stock1_code
                comparison_info["reason"] = f"两股票均在中枢上方，{stock1_name}({stock1_code})中枢能量密度{energy_density1:.2f} > {stock2_name}({stock2_code})中枢能量密度{energy_density2:.2f}，前者获胜"
                return stock1_code, comparison_info
            elif energy_density2 > energy_density1:
                comparison_info["winner"] = stock2_code
                comparison_info["reason"] = f"两股票均在中枢上方，{stock2_name}({stock2_code})中枢能量密度{energy_density2:.2f} > {stock1_name}({stock1_code})中枢能量密度{energy_density1:.2f}，前者获胜"
                return stock2_code, comparison_info
            else:
                comparison_info["winner"] = "tie"
                comparison_info["reason"] = f"两股票均在中枢上方且中枢能量密度相等({energy_density1:.2f})，判定平局"
                return "tie", comparison_info

        # 情况3：如两只股票都在中枢上沿之下，按照新接口的"盈亏比"数值逻辑对比
        else:
            # 使用新接口的盈亏比计算方法（取算法1和算法2中更大的那个）
            ratio1 = result1.get('profit_loss_ratio_final', 0)
            ratio2 = result2.get('profit_loss_ratio_final', 0)

            comparison_info["case"] = "both_below_zs"
            comparison_info["ratio1"] = ratio1
            comparison_info["ratio2"] = ratio2

            if ratio1 > ratio2:
                comparison_info["winner"] = stock1_code
                comparison_info["reason"] = f"两股票均在中枢下方，{stock1_name}({stock1_code})盈亏比{ratio1:.4f} > {stock2_name}({stock2_code})盈亏比{ratio2:.4f}，前者获胜"
                return stock1_code, comparison_info
            elif ratio2 > ratio1:
                comparison_info["winner"] = stock2_code
                comparison_info["reason"] = f"两股票均在中枢下方，{stock2_name}({stock2_code})盈亏比{ratio2:.4f} > {stock1_name}({stock1_code})盈亏比{ratio1:.4f}，前者获胜"
                return stock2_code, comparison_info
            else:
                comparison_info["winner"] = "tie"
                comparison_info["reason"] = f"两股票均在中枢下方且盈亏比相等({ratio1:.4f})，判定平局"
                return "tie", comparison_info

    except Exception as e:
        error_msg = f"盈亏比对比异常: {str(e)}"
        print(error_msg)
        return _compare_profit_loss_ratio_fallback(stock1_code, stock1_name, stock2_code, stock2_name)


def _compare_profit_loss_ratio_with_find3buy(stock1_code, stock1_name, stock2_code, stock2_name):
    """
    使用原有find3buy方法的盈亏比对比函数（降级方法）
    """
    try:
        if not CHAN_ANALYSIS_AVAILABLE:
            return "tie", {"error": "缠论分析模块不可用", "method": "unavailable"}

        # 参数验证
        if not stock1_code or not stock2_code:
            return "tie", {"error": "股票代码不能为空", "method": "parameter_error"}

        # 获取两只股票的缠论分析结果，使用 find3buy 的方法
        try:
            from Common.CEnum import KL_TYPE
        except ImportError:
            try:
                from libs.chan.Common.CEnum import KL_TYPE
            except ImportError:
                return "tie", {"error": "无法导入KL_TYPE", "method": "import_error"}

        # 使用 find3buy 的 check_3buy_conditions_debug 方法获取详细分析结果
        result1 = Zstock_find3buy.check_3buy_conditions_debug(
            stock1_code, stock1_name or stock1_code, k_type=KL_TYPE.K_DAY
        )
        result2 = Zstock_find3buy.check_3buy_conditions_debug(
            stock2_code, stock2_name or stock2_code, k_type=KL_TYPE.K_DAY
        )

        if not (result1 and result2):
            # 如果任一股票分析失败，使用降级方法
            return _compare_profit_loss_ratio_fallback(stock1_code, stock1_name, stock2_code, stock2_name)

        # 提取关键信息
        zs_upper1 = result1.get('zs_upper', 0)
        zs_upper2 = result2.get('zs_upper', 0)
        current_price1 = result1.get('price_for_judgment', 0)
        current_price2 = result2.get('price_for_judgment', 0)

        # 用find3buy的方法判断现价是否在最近的中枢上沿之上
        above_zs1 = current_price1 > zs_upper1 if zs_upper1 > 0 else False
        above_zs2 = current_price2 > zs_upper2 if zs_upper2 > 0 else False

        comparison_info = {
            "stock1_info": {
                "code": stock1_code,
                "name": stock1_name,
                "current_price": current_price1,
                "zs_upper": zs_upper1,
                "above_zs": above_zs1
            },
            "stock2_info": {
                "code": stock2_code,
                "name": stock2_name,
                "current_price": current_price2,
                "zs_upper": zs_upper2,
                "above_zs": above_zs2
            },
            "method": "find3buy_fallback_analysis"
        }

        # 情况1：如两只股票有一只在上沿之上，有一只不在，在中枢上沿之上的那个股票直接获胜
        if above_zs1 and not above_zs2:
            comparison_info["case"] = "position_advantage"
            comparison_info["winner"] = stock1_code
            comparison_info["reason"] = f"{stock1_name}({stock1_code})现价{current_price1:.2f}位于中枢上沿{zs_upper1:.2f}之上，而{stock2_name}({stock2_code})现价{current_price2:.2f}位于中枢上沿{zs_upper2:.2f}之下，前者获胜"
            return stock1_code, comparison_info
        elif above_zs2 and not above_zs1:
            comparison_info["case"] = "position_advantage"
            comparison_info["winner"] = stock2_code
            comparison_info["reason"] = f"{stock2_name}({stock2_code})现价{current_price2:.2f}位于中枢上沿{zs_upper2:.2f}之上，而{stock1_name}({stock1_code})现价{current_price1:.2f}位于中枢上沿{zs_upper1:.2f}之下，前者获胜"
            return stock2_code, comparison_info

        # 情况2：如两只股票都在中枢上沿之上，直接对比来自 find3buy 定义的"中枢能量密度"值
        elif above_zs1 and above_zs2:
            # 使用 find3buy 的中枢能量密度计算方法
            energy_density1 = _calculate_find3buy_energy_density(result1)
            energy_density2 = _calculate_find3buy_energy_density(result2)

            comparison_info["case"] = "both_above_zs"
            comparison_info["energy_density1"] = energy_density1
            comparison_info["energy_density2"] = energy_density2

            if energy_density1 > energy_density2:
                comparison_info["winner"] = stock1_code
                comparison_info["reason"] = f"两股票均在中枢上方，{stock1_name}({stock1_code})中枢能量密度{energy_density1:.2f} > {stock2_name}({stock2_code})中枢能量密度{energy_density2:.2f}，前者获胜"
                return stock1_code, comparison_info
            elif energy_density2 > energy_density1:
                comparison_info["winner"] = stock2_code
                comparison_info["reason"] = f"两股票均在中枢上方，{stock2_name}({stock2_code})中枢能量密度{energy_density2:.2f} > {stock1_name}({stock1_code})中枢能量密度{energy_density1:.2f}，前者获胜"
                return stock2_code, comparison_info
            else:
                comparison_info["winner"] = "tie"
                comparison_info["reason"] = f"两股票均在中枢上方且中枢能量密度相等({energy_density1:.2f})，判定平局"
                return "tie", comparison_info

        # 情况3：如两只股票都在中枢上沿之下，按照 find3buy程序里的"盈亏比"数值逻辑对比
        else:
            # 使用 find3buy 的盈亏比计算方法（取算法1和算法2中更大的那个）
            ratio1 = _calculate_find3buy_profit_loss_ratio(result1)
            ratio2 = _calculate_find3buy_profit_loss_ratio(result2)

            comparison_info["case"] = "both_below_zs"
            comparison_info["ratio1"] = ratio1
            comparison_info["ratio2"] = ratio2

            if ratio1 > ratio2:
                comparison_info["winner"] = stock1_code
                comparison_info["reason"] = f"两股票均在中枢下方，{stock1_name}({stock1_code})盈亏比{ratio1:.4f} > {stock2_name}({stock2_code})盈亏比{ratio2:.4f}，前者获胜"
                return stock1_code, comparison_info
            elif ratio2 > ratio1:
                comparison_info["winner"] = stock2_code
                comparison_info["reason"] = f"两股票均在中枢下方，{stock2_name}({stock2_code})盈亏比{ratio2:.4f} > {stock1_name}({stock1_code})盈亏比{ratio1:.4f}，前者获胜"
                return stock2_code, comparison_info
            else:
                comparison_info["winner"] = "tie"
                comparison_info["reason"] = f"两股票均在中枢下方且盈亏比相等({ratio1:.4f})，判定平局"
                return "tie", comparison_info

    except Exception as e:
        error_msg = f"降级盈亏比对比异常: {str(e)}"
        print(error_msg)
        return _compare_profit_loss_ratio_fallback(stock1_code, stock1_name, stock2_code, stock2_name)

def _calculate_find3buy_energy_density(chan_result):
    """
    计算中枢能量密度，使用 find3buy 程序中的定义

    中枢能量密度 = 中枢内K线数量 / 中枢相对振幅
    """
    try:
        # 从缠论分析结果中提取相关信息
        zs_upper = chan_result.get('zs_upper', 0)
        zs_lower = chan_result.get('zs_lower', 0)

        if zs_upper > zs_lower > 0:
            # 使用简化的计算方法
            # 根据经验，中枢持续时间通常占总K线数的20-40%
            kline_count = chan_result.get('kline_count', 100)
            estimated_zs_kline_count = max(20, int(kline_count * 0.3))  # 估算中枢K线数为总数的30%

            # 中枢中间价
            zs_mid_price = (zs_upper + zs_lower) / 2

            # 中枢相对振幅
            zs_relative_amplitude = (zs_upper - zs_lower) / zs_mid_price

            # 中枢能量密度
            if zs_relative_amplitude > 0:
                energy_density = estimated_zs_kline_count / zs_relative_amplitude
                return energy_density

        return 0.0
    except Exception as e:
        print(f"计算中枢能量密度出错: {e}")
        return 0.0

def _calculate_find3buy_profit_loss_ratio(chan_result):
    """
    计算盈亏比，使用 find3buy 程序中的逻辑（取算法1和算法2中更大的那个）

    从 chan_result 中提取盈亏比信息，如果有 ratio_details，则使用其中的 final_ratio
    否则尝试使用 profit_loss_ratio 字段
    """
    try:
        # 首先尝试从 ratio_details 中获取最终盈亏比
        ratio_details = chan_result.get('ratio_details', {})
        if ratio_details and 'final_ratio' in ratio_details:
            return ratio_details['final_ratio']

        # 如果没有 ratio_details，尝试直接获取 profit_loss_ratio
        profit_loss_ratio = chan_result.get('profit_loss_ratio', 0)
        if isinstance(profit_loss_ratio, (int, float)):
            return profit_loss_ratio

        # 如果都没有，返回0
        return 0.0
    except Exception:
        return 0.0

def _calculate_zs_energy_density(chan_result):
    """
    计算中枢能量密度（保留原函数名用于兼容性）
    现在调用新的 find3buy 能量密度计算方法
    """
    return _calculate_find3buy_energy_density(chan_result)

def _compare_profit_loss_ratio_fallback(stock1_code, stock1_name, stock2_code, stock2_name):
    """
    盈亏比对比的降级方法
    """
    try:
        # 参数验证
        if not stock1_code or not stock2_code:
            return "tie", {"error": "股票代码不能为空", "method": "fallback_parameter_error"}

        # 使用原有的简单盈亏比计算进行对比
        ratio1, details1 = stock_score.calculate_profit_loss_ratio_new(stock1_code, stock1_name)
        ratio2, details2 = stock_score.calculate_profit_loss_ratio_new(stock2_code, stock2_name)

        comparison_info = {
            "method": "fallback_comparison",
            "ratio1": ratio1,
            "ratio2": ratio2,
            "details1": details1,
            "details2": details2
        }

        if ratio1 > ratio2:
            comparison_info["winner"] = stock1_code
            comparison_info["reason"] = f"降级模式：{stock1_name}({stock1_code})盈亏比{ratio1:.4f} > {stock2_name}({stock2_code})盈亏比{ratio2:.4f}"
            return stock1_code, comparison_info
        elif ratio2 > ratio1:
            comparison_info["winner"] = stock2_code
            comparison_info["reason"] = f"降级模式：{stock2_name}({stock2_code})盈亏比{ratio2:.4f} > {stock1_name}({stock1_code})盈亏比{ratio1:.4f}"
            return stock2_code, comparison_info
        else:
            comparison_info["winner"] = "tie"
            comparison_info["reason"] = f"降级模式：两股票盈亏比相等({ratio1:.4f})"
            return "tie", comparison_info

    except Exception as e:
        return "tie", {"error": f"降级对比失败: {str(e)}", "method": "fallback_failed"}

def compare_fund_score_new(stock1_code, stock1_name, stock2_code, stock2_name, capital_info1, capital_info2):
    """
    新的资金面对比函数，实现用户要求的对比逻辑

    对比逻辑：
    1. 分别计算两只股票的"资金活跃度"和"主力/主买净流入天数"两个子维度的得分
    2. 对比两只股票的子维度分数之和（总分范围0-2分）
    3. 总分高的股票获胜；如果总分相等，则该维度判定为平局

    返回:
        (胜者代码, 详细对比信息字典)
    """
    try:
        # 参数验证
        if not stock1_code or not stock2_code:
            return "tie", {"error": "股票代码不能为空", "method": "parameter_error"}

        # 计算两只股票的资金面评分（包含两个子维度）
        score1, reason1, details1 = stock_score.calculate_fund_score_detailed(capital_info1, stock1_code, stock1_name)
        score2, reason2, details2 = stock_score.calculate_fund_score_detailed(capital_info2, stock2_code, stock2_name)

        # 提取子维度得分
        activity_score1 = details1.get('activity_score', 0) if isinstance(details1, dict) else 0
        flow_score1 = details1.get('flow_score', 0) if isinstance(details1, dict) else 0
        total_score1 = activity_score1 + flow_score1

        activity_score2 = details2.get('activity_score', 0) if isinstance(details2, dict) else 0
        flow_score2 = details2.get('flow_score', 0) if isinstance(details2, dict) else 0
        total_score2 = activity_score2 + flow_score2

        comparison_info = {
            "method": "fund_score_comparison",
            "stock1_info": {
                "code": stock1_code,
                "name": stock1_name,
                "activity_score": activity_score1,
                "flow_score": flow_score1,
                "total_score": total_score1,
                "final_score": score1,
                "reason": reason1,
                "details": details1
            },
            "stock2_info": {
                "code": stock2_code,
                "name": stock2_name,
                "activity_score": activity_score2,
                "flow_score": flow_score2,
                "total_score": total_score2,
                "final_score": score2,
                "reason": reason2,
                "details": details2
            }
        }

        # 对比总分
        if total_score1 > total_score2:
            comparison_info["winner"] = stock1_code
            comparison_info["reason"] = f"{stock1_name}({stock1_code})资金面总分{total_score1}分(资金活跃度{activity_score1}分+主力净流入{flow_score1}分) > {stock2_name}({stock2_code})总分{total_score2}分(资金活跃度{activity_score2}分+主力净流入{flow_score2}分)，前者获胜"
            return stock1_code, comparison_info
        elif total_score2 > total_score1:
            comparison_info["winner"] = stock2_code
            comparison_info["reason"] = f"{stock2_name}({stock2_code})资金面总分{total_score2}分(资金活跃度{activity_score2}分+主力净流入{flow_score2}分) > {stock1_name}({stock1_code})总分{total_score1}分(资金活跃度{activity_score1}分+主力净流入{flow_score1}分)，前者获胜"
            return stock2_code, comparison_info
        else:
            comparison_info["winner"] = "tie"
            comparison_info["reason"] = f"两股票资金面总分相等({total_score1}分)，判定平局"
            return "tie", comparison_info

    except Exception as e:
        error_msg = f"资金面对比异常: {str(e)}"
        print(error_msg)
        return _compare_fund_score_fallback(stock1_code, stock1_name, stock2_code, stock2_name, capital_info1, capital_info2)

def _compare_fund_score_fallback(stock1_code, stock1_name, stock2_code, stock2_name, capital_info1, capital_info2):
    """
    资金面对比的降级方法
    """
    try:
        # 参数验证
        if not stock1_code or not stock2_code:
            return "tie", {"error": "股票代码不能为空", "method": "fallback_parameter_error"}

        # 使用原有的简单资金面计算进行对比
        score1, reason1, details1 = stock_score.calculate_fund_flow_score_detailed(capital_info1)
        score2, reason2, details2 = stock_score.calculate_fund_flow_score_detailed(capital_info2)

        comparison_info = {
            "method": "fallback_fund_comparison",
            "score1": score1,
            "score2": score2,
            "reason1": reason1,
            "reason2": reason2,
            "details1": details1,
            "details2": details2
        }

        if score1 > score2:
            comparison_info["winner"] = stock1_code
            comparison_info["reason"] = f"降级模式：{stock1_name}({stock1_code})资金面得分{score1} > {stock2_name}({stock2_code})得分{score2}"
            return stock1_code, comparison_info
        elif score2 > score1:
            comparison_info["winner"] = stock2_code
            comparison_info["reason"] = f"降级模式：{stock2_name}({stock2_code})资金面得分{score2} > {stock1_name}({stock1_code})得分{score1}"
            return stock2_code, comparison_info
        else:
            # 分数相同，比较正向天数
            positive_days1 = details1.get('positive_days', 0) if isinstance(details1, dict) else 0
            positive_days2 = details2.get('positive_days', 0) if isinstance(details2, dict) else 0

            if positive_days1 > positive_days2:
                comparison_info["winner"] = stock1_code
                comparison_info["reason"] = f"降级模式：得分相同但{stock1_name}({stock1_code})正向天数{positive_days1} > {stock2_name}({stock2_code})正向天数{positive_days2}"
                return stock1_code, comparison_info
            elif positive_days2 > positive_days1:
                comparison_info["winner"] = stock2_code
                comparison_info["reason"] = f"降级模式：得分相同但{stock2_name}({stock2_code})正向天数{positive_days2} > {stock1_name}({stock1_code})正向天数{positive_days1}"
                return stock2_code, comparison_info
            else:
                comparison_info["winner"] = "tie"
                comparison_info["reason"] = f"降级模式：两股票得分和正向天数均相等，判定平局"
                return "tie", comparison_info

    except Exception as e:
        return "tie", {"error": f"降级资金面对比失败: {str(e)}", "method": "fallback_failed"}

def calculate_profit_loss_ratio_new(stock_code, stock_name=None):
    """
    保持原有的盈亏比计算函数以兼容现有代码
    """
    # 参数验证
    if not stock_code or not isinstance(stock_code, str) or len(stock_code.strip()) == 0:
        return 0.0, {"error": "股票代码不能为空或无效", "method": "parameter_error"}

    return stock_score.calculate_profit_loss_ratio_new(stock_code, stock_name)

def _calculate_profit_loss_ratio_fallback(stock_code):
    """
    盈亏比计算的降级方法
    当缠论分析不可用时使用简化算法
    """
    try:
        # 尝试获取基本的技术指标信息
        tech_info = stock_tech.analyze_stock(stock_code)

        if isinstance(tech_info, str) and "error" not in tech_info.lower():
            # 从技术指标中提取压力位空间信息作为简化的盈亏比指标
            pressure_pct = _extract_pressure_space_from_tech_info(tech_info)

            if pressure_pct > 0:
                # 将压力位空间转换为简化的盈亏比
                # 压力位空间越大，盈亏比越高
                simplified_ratio = pressure_pct / 10.0  # 10%压力位空间对应1.0盈亏比
                return simplified_ratio, {
                    "method": "simplified_pressure_space",
                    "pressure_space_pct": pressure_pct,
                    "calculation": f"压力位空间{pressure_pct}% / 10 = {simplified_ratio:.2f}"
                }

        # 如果无法获取任何有效信息，返回默认值
        return 0.0, {
            "error": "无法获取技术指标信息",
            "method": "default_fallback"
        }

    except Exception as e:
        return 0.0, {
            "error": f"降级计算失败: {str(e)}",
            "method": "fallback_failed"
        }

def _extract_pressure_space_from_tech_info(tech_info: str) -> float:
    """从技术指标信息中提取压力位空间百分比（复制自Zstock_score0714.py）"""
    import re
    if not isinstance(tech_info, str):
        return 0.0

    # 匹配格式：   - 压力位1: 856.82 (0.49%上涨空间), 强度: 2
    pattern = r"\s*-\s*压力位\d+:\s*[\d\.]+\s*\(([\d\.]+)%上涨空间\)"
    matches = re.findall(pattern, tech_info)

    if matches:
        try:
            # 返回第一个压力位的上涨空间百分比
            return float(matches[0])
        except (ValueError, TypeError):
            pass

    # 如果没有找到准确格式，尝试其他可能的格式
    # 匹配格式：压力位空间：X.X%
    pattern2 = r"压力位空间[：:]\s*([\d\.]+)%"
    matches2 = re.findall(pattern2, tech_info)
    if matches2:
        try:
            return float(matches2[0])
        except (ValueError, TypeError):
            pass

    # 匹配格式：距压力位空间X.X%
    pattern3 = r"距压力位空间\s*([\d\.]+)%"
    matches3 = re.findall(pattern3, tech_info)
    if matches3:
        try:
            return float(matches3[0])
        except (ValueError, TypeError):
            pass

    return 0.0

# ====================== 新增：多维度拆分对比相关常量及函数 =====================

# 8 个对比维度定义（第 6 个索引 6 为"盈亏比对比"，第 7 个索引 7 为"基本面对比"，其余维度需调用 LLM）
# 注意：移除了"超短趋势对比"维度，第1个维度"中线趋势判断"只使用中线趋势对比
DIMENSION_DEFINITIONS = [
    {"key": "短线趋势对比", "abbr": "短线趋势", "keywords": ["短线趋势", "短线技术指标", "日线"]},
    {"key": "中线趋势判断", "abbr": "中线趋势", "keywords": ["中线趋势", "中线技术指标", "周线"]},
    {"key": "资金面对比", "abbr": "资金面", "keywords": ["资金面情况", "主力净额", "主买净额"]},
    {"key": "消息面对比", "abbr": "消息面", "keywords": ["相关新闻", "研报", "消息"]},
    {"key": "行业趋势及共振对比", "abbr": "行业趋势及共振", "keywords": ["行业技术指标", "行业", "板块", "所属行业技术指标", "行业共振趋势", "放量突破"]},
    {"key": "盈亏比对比", "abbr": "盈亏比", "keywords": ["中枢", "上沿", "缠论", "盈亏比"]},
    {"key": "基本面对比", "abbr": "基本面", "keywords": ["PE分位", "ROE", "季度增长率", "基本面", "财务指标"]}
]


def process_single_dimension(dim, stock1_code, stock1_name, stock2_code, stock2_name,
                           tech_info1, tech_info2, capital_info1, capital_info2,
                           industry_info1, industry_info2, resonance_info1, resonance_info2,
                           client, fundamental_cache, stock_item_scores_map, stock_extra_map,
                           stock_to_industry=None, industry_tech_info=None, model_type=None):
    """
    处理单个维度的对比
    返回: (dim_key, result, details)
    """
    dim_key = dim['key']

    # 初始化维度详情
    details = {
        'prompt': '',
        'response': '',
        'error': None,
        'processing_time': 0,
        'retries_used': 0
    }

    try:
        if dim_key == '盈亏比对比':
            # 使用新的盈亏比对比逻辑
            start_time = time.time()
            print(f"[DEBUG] 盈亏比对比: 开始处理 {stock1_code} vs {stock2_code}")

            # 使用新的盈亏比对比函数
            result, comparison_info = compare_profit_loss_ratio_new(stock1_code, stock1_name, stock2_code, stock2_name)
            print(f"[DEBUG] 盈亏比对比: 函数返回结果={result}, comparison_info keys={list(comparison_info.keys())}")

            end_time = time.time()
            processing_time = end_time - start_time

            # 构建详细信息
            details['prompt'] = f"盈亏比对比: {stock1_code} vs {stock2_code}"
            details['response'] = f"""盈亏比对比结果:
对比方法: {comparison_info.get('method', 'unknown')}
对比情况: {comparison_info.get('case', 'unknown')}
对比结果: {comparison_info.get('reason', 'unknown')}

股票1详情:
  代码: {comparison_info.get('stock1_info', {}).get('code', 'N/A')}
  当前价格: {comparison_info.get('stock1_info', {}).get('current_price', 'N/A')}
  中枢上沿: {comparison_info.get('stock1_info', {}).get('zs_upper', 'N/A')}
  位于中枢上方: {comparison_info.get('stock1_info', {}).get('above_zs', 'N/A')}

股票2详情:
  代码: {comparison_info.get('stock2_info', {}).get('code', 'N/A')}
  当前价格: {comparison_info.get('stock2_info', {}).get('current_price', 'N/A')}
  中枢上沿: {comparison_info.get('stock2_info', {}).get('zs_upper', 'N/A')}
  位于中枢上方: {comparison_info.get('stock2_info', {}).get('above_zs', 'N/A')}"""
            details['processing_time'] = processing_time
            details['comparison_info'] = comparison_info
            print(f"[DEBUG] 盈亏比对比: details['comparison_info'] keys={list(details['comparison_info'].keys())}")

            return (dim_key, result, details)

        elif dim_key == '基本面对比':
            # 使用缓存的基本面数据进行对比
            fundamental_data1 = fundamental_cache.get(stock1_code, f"股票 {stock1_code} 基本面数据未缓存")
            fundamental_data2 = fundamental_cache.get(stock2_code, f"股票 {stock2_code} 基本面数据未缓存")

            # 构建基本面对比prompt
            prompt = _build_dimension_prompt(dim, stock1_code, stock1_name, fundamental_data1, stock2_code, stock2_name, fundamental_data2)
            details['prompt'] = prompt

            start_time = time.time()
            result_text = client.send(prompt)
            end_time = time.time()
            processing_time = end_time - start_time

            details['response'] = result_text
            details['processing_time'] = processing_time
            details['retries_used'] = 0

            # 解析结果
            parsed_result = _parse_dimension_result(result_text, stock1_code, stock2_code)
            return (dim_key, parsed_result, details)

        elif dim_key == '资金面对比':
            # 使用新的资金面对比逻辑
            start_time = time.time()

            # 使用新的资金面对比函数
            result, comparison_info = compare_fund_score_new(stock1_code, stock1_name, stock2_code, stock2_name, capital_info1, capital_info2)

            end_time = time.time()
            processing_time = end_time - start_time

            # 构建详细信息
            details['prompt'] = f"资金面对比: {stock1_code} vs {stock2_code}"
            details['response'] = f"""资金面对比结果:
对比方法: {comparison_info.get('method', 'unknown')}
对比结果: {comparison_info.get('reason', 'unknown')}

股票1详情:
  代码: {comparison_info.get('stock1_info', {}).get('code', 'N/A')}
  资金活跃度: {comparison_info.get('stock1_info', {}).get('activity_score', 'N/A')}分
  主力净流入: {comparison_info.get('stock1_info', {}).get('flow_score', 'N/A')}分
  总分: {comparison_info.get('stock1_info', {}).get('total_score', 'N/A')}分

股票2详情:
  代码: {comparison_info.get('stock2_info', {}).get('code', 'N/A')}
  资金活跃度: {comparison_info.get('stock2_info', {}).get('activity_score', 'N/A')}分
  主力净流入: {comparison_info.get('stock2_info', {}).get('flow_score', 'N/A')}分
  总分: {comparison_info.get('stock2_info', {}).get('total_score', 'N/A')}分"""
            details['processing_time'] = processing_time
            details['comparison_info'] = comparison_info

            return (dim_key, result, details)

        elif dim_key == '消息面对比':
            # 检查是否为模式1-4，如果是则直接返回平局
            if model_type in ["deepseek", "doubao", "doubao-thinking", "deepseek-reasoner"]:
                # 模式1-4：暂时不对消息面做对比，默认都算作平局
                result = 'tie'
                details['prompt'] = '模式1-4暂时不对消息面做对比'
                details['response'] = '根据用户要求，模式1-4中消息面对比暂时默认为平局'
                details['processing_time'] = 0.0
                details['retries_used'] = 0
                return (dim_key, result, details)
            else:
                # 模式5-6：使用已获取的消息面信息进行本地对比
                news_info1 = stock_extra_map.get(stock1_code, {}).get('消息面判断', {})
                news_info2 = stock_extra_map.get(stock2_code, {}).get('消息面判断', {})

                # 获取消息面得分
                score1 = stock_item_scores_map.get(stock1_code, {}).get('消息面判断', {}).get('score', 0)
                score2 = stock_item_scores_map.get(stock2_code, {}).get('消息面判断', {}).get('score', 0)

                # 比较消息面得分
                if score1 > score2:
                    result = stock1_code
                elif score2 > score1:
                    result = stock2_code
                else:
                    # 得分相同，进行二级比较（方向和强度）
                    dir1 = news_info1.get("direction", "中性")
                    str1 = news_info1.get("strength", "中性")
                    dir2 = news_info2.get("direction", "中性")
                    str2 = news_info2.get("strength", "中性")

                    def get_direction_priority(direction):
                        if direction == "利多":
                            return 2
                        elif direction == "中性":
                            return 1
                        else:  # 利空
                            return 0

                    def get_strength_priority(strength):
                        if strength == "强":
                            return 2
                        elif strength == "弱":
                            return 1
                        else:  # 中性
                            return 0

                    priority1 = get_direction_priority(dir1) * 10 + get_strength_priority(str1)
                    priority2 = get_direction_priority(dir2) * 10 + get_strength_priority(str2)

                    if priority1 > priority2:
                        result = stock1_code
                    elif priority2 > priority1:
                        result = stock2_code
                    else:
                        result = 'tie'

                details['prompt'] = '本地计算模式（使用已获取的消息面信息）'
                details['response'] = f'股票1得分:{score1}(方向:{news_info1.get("direction", "中性")},强度:{news_info1.get("strength", "中性")}), 股票2得分:{score2}(方向:{news_info2.get("direction", "中性")},强度:{news_info2.get("strength", "中性")})'
                details['processing_time'] = 0.0
                details['retries_used'] = 0
                return (dim_key, result, details)

        else:
            # 其他维度使用LLM处理 - 根据维度选择合适的信息源
            if dim_key in ('短线趋势对比',):
                src1 = tech_info1
                src2 = tech_info2
            elif dim_key == '资金面对比':
                src1 = capital_info1
                src2 = capital_info2
            elif dim_key == '行业趋势及共振对比':
                # 提取行业技术指标信息
                industry_trend_info1 = ""
                industry_trend_info2 = ""

                if stock_to_industry and industry_tech_info:
                    full_ind1 = stock_to_industry.get(stock1_code, "")
                    full_ind2 = stock_to_industry.get(stock2_code, "")

                    ind1 = full_ind1.split('-')[-1] if '-' in full_ind1 else full_ind1
                    ind2 = full_ind2.split('-')[-1] if '-' in full_ind2 else full_ind2

                    industry_trend_info1 = extract_industry_trend_info(industry_tech_info, ind1)
                    industry_trend_info2 = extract_industry_trend_info(industry_tech_info, ind2)

                # 合并行业技术指标信息和共振信息
                src1 = f"行业趋势及共振信息：\n{industry_trend_info1}\n\n行业共振情况：{resonance_info1}"
                src2 = f"行业趋势及共振信息：\n{industry_trend_info2}\n\n行业共振情况：{resonance_info2}"
            elif dim_key == '中线趋势判断':
                # 中线趋势判断：只使用中线趋势对比，不再合并超短趋势对比
                src1 = tech_info1
                src2 = tech_info2
            else:
                # 默认使用技术指标信息
                src1 = tech_info1
                src2 = tech_info2

            # 特殊处理：行业趋势及共振对比直接使用原始信息，不进行关键词过滤
            if dim_key == '行业趋势及共振对比':
                filtered_info1 = src1
                filtered_info2 = src2
            else:
                # 过滤相关信息
                filtered_info1 = _filter_relevant_info(src1, dim['keywords'])
                filtered_info2 = _filter_relevant_info(src2, dim['keywords'])

            # 构建prompt
            prompt = _build_dimension_prompt(dim, stock1_code, stock1_name, filtered_info1, stock2_code, stock2_name, filtered_info2)
            details['prompt'] = prompt

            start_time = time.time()
            result_text = client.send(prompt)
            end_time = time.time()
            processing_time = end_time - start_time

            details['response'] = result_text
            details['processing_time'] = processing_time
            details['retries_used'] = 0

            # 解析结果
            parsed_result = _parse_dimension_result(result_text, stock1_code, stock2_code)
            return (dim_key, parsed_result, details)

    except Exception as e:
        details['error'] = str(e)
        print(f"处理维度 {dim_key} 失败: {e}")
        return (dim_key, None, details)


def extract_industry_trend_info(industry_tech_info, industry_name):
    """从行业技术指标信息中提取短线趋势相关的段落"""
    if not industry_tech_info or not industry_name:
        return ""

    if industry_name not in industry_tech_info:
        return ""

    full_text = industry_tech_info[industry_name]
    if not full_text:
        return ""

    # 查找"短线趋势行情/短线技术指标"开始的段落
    start_markers = ["短线趋势行情", "短线技术指标"]
    end_markers = ["中线趋势行情", "中线技术指标", "超短线趋势行情", "超短线技术指标", "---", "===="]

    lines = full_text.split('\n')
    start_idx = -1
    end_idx = len(lines)

    # 查找开始位置
    for i, line in enumerate(lines):
        for marker in start_markers:
            if marker in line:
                start_idx = i
                break
        if start_idx != -1:
            break

    # 如果没找到开始标记，返回空
    if start_idx == -1:
        return ""

    # 查找结束位置
    for i in range(start_idx + 1, len(lines)):
        for marker in end_markers:
            if marker in lines[i]:
                end_idx = i
                break
        if end_idx != len(lines):
            break

    # 提取相关段落
    relevant_lines = lines[start_idx:end_idx]
    return '\n'.join(relevant_lines)


def _parse_positive_days(reason_text: str) -> int:
    """[Fallback] 从资金面决策文本中提取合计正向天数。"""
    import re
    if not isinstance(reason_text, str):
        return 0
    m = re.search(r"合计(\d+)天", reason_text)
    if m:
        try:
            return int(m.group(1))
        except (ValueError, TypeError):
            return 0
    return 0


def get_fundamental_data_for_comparison(stock_code):
    """
    获取股票基本面数据用于对比
    返回格式化的基本面数据字符串
    """
    try:
        # 导入基本面分析模块
        import Zstock_score0724 as stock_score

        # 获取基本面数据
        score, decision_process, details = stock_score.calculate_fundamental_score_detailed(stock_code)

        if 'error' in details:
            return f"股票 {stock_code} 基本面数据获取失败: {details['error']}"

        # 构建格式化的基本面数据字符串
        fundamental_text = f"股票 {stock_code} 基本面数据:\n"

        # PE分析结果
        if 'pe_data' in details:
            pe_data = details['pe_data']
            if 'error' not in pe_data:
                fundamental_text += f"a. 市盈率分析结果\n"
                fundamental_text += f"- 当前扣非PE: {pe_data.get('current_pe', 'N/A')}\n"
                fundamental_text += f"- PE-TTM近3年百分位: {pe_data.get('percentile', 'N/A')}%\n"
                fundamental_text += f"- 最新数据日期: {pe_data.get('latest_date', 'N/A')}\n"
            else:
                fundamental_text += f"a. 市盈率分析结果\n"
                fundamental_text += f"查询失败: {pe_data.get('error', '未知错误')}\n"

        # ROE分析结果
        if 'roe_data' in details:
            roe_data = details['roe_data']
            if 'error' not in roe_data:
                fundamental_text += f"\nc. ROE分析结果\n"
                fundamental_text += f"- ROE：{roe_data.get('roe', 'N/A')}%\n"
                fundamental_text += f"- 计算时间：{roe_data.get('calculation_date', 'N/A')}\n"
            else:
                fundamental_text += f"\nc. ROE分析结果\n"
                fundamental_text += f"查询失败: ROE数据获取失败\n"

        # 季度增长率分析结果
        if 'quarterly_data' in details:
            quarterly_result = details['quarterly_data']
            if 'error' not in quarterly_result and 'quarterly_data' in quarterly_result:
                fundamental_text += f"\nb. 季度增长率分析结果\n"
                fundamental_text += f"\n营收季度增长率（同比）\n"
                for quarter in quarterly_result['quarterly_data']:
                    quarter_str = quarter['quarter']
                    revenue_growth = quarter['revenue_growth']
                    revenue_str = f"{revenue_growth:+.2f}%" if revenue_growth is not None else "N/A"
                    fundamental_text += f"- {quarter_str}：{revenue_str}\n"

                fundamental_text += f"\n扣非利润增长率（同比）\n"
                for quarter in quarterly_result['quarterly_data']:
                    quarter_str = quarter['quarter']
                    profit_growth = quarter['profit_growth']
                    profit_str = f"{profit_growth:+.2f}%" if profit_growth is not None else "N/A"
                    fundamental_text += f"- {quarter_str}：{profit_str}\n"
            else:
                fundamental_text += f"\nb. 季度增长率分析结果\n"
                fundamental_text += f"查询失败: 季度数据获取失败\n"



        return fundamental_text

    except Exception as e:
        return f"股票 {stock_code} 基本面数据获取异常: {str(e)}"


def extract_industry_trend_info(industry_tech_info, industry_name):
    """从行业技术指标信息中提取短线趋势相关的段落"""
    if not industry_tech_info or not industry_name:
        return ""
    
    if industry_name not in industry_tech_info:
        return ""
    
    full_text = industry_tech_info[industry_name]
    if not full_text:
        return ""
    
    # 查找"短线趋势行情/短线技术指标"开始的段落
    start_markers = ["短线趋势行情", "短线技术指标"]
    end_markers = ["中线趋势行情", "中线技术指标", "超短线趋势行情", "超短线技术指标", "---", "===="]
    
    lines = full_text.split('\n')
    start_idx = -1
    end_idx = len(lines)
    
    # 查找开始位置
    for i, line in enumerate(lines):
        if any(marker in line for marker in start_markers):
            start_idx = i
            break
    
    if start_idx == -1:
        return ""
    
    # 查找结束位置
    for i in range(start_idx + 1, len(lines)):
        line = lines[i]
        if any(marker in line for marker in end_markers):
            end_idx = i
            break
    
    # 提取相关段落
    extracted_lines = lines[start_idx:end_idx]
    return '\n'.join(extracted_lines)

def _filter_relevant_info(raw_text: str, keywords: list) -> str:
    """提取包含任意关键词的行，若未找到则返回原文（防止信息缺失）。"""
    if not raw_text:
        return ""
    
    # 对于行业趋势相关的关键词，需要特殊处理
    # 因为行业信息可能以"行业板块 'xxx' (代码: xxx) 的技术指标分析："开头
    is_industry_dimension = any(k in keywords for k in ["行业技术指标", "行业", "板块", "所属行业技术指标"])
    
    if is_industry_dimension and "行业板块" in raw_text:
        # 如果是行业维度且包含"行业板块"，直接返回原文（不截断）
        return raw_text
    
    lines = raw_text.splitlines()
    filtered = []
    in_block = False  # 是否处于匹配段落
    
    # 检查是否为短线趋势维度，需要排除超短线信息
    is_short_trend = any(k in keywords for k in ["短线趋势", "短线技术指标"]) and "超短线" not in " ".join(keywords)
    
    for line in lines:
        # 如果是短线趋势维度，排除包含"超短线"的行
        if is_short_trend and "超短线" in line:
            in_block = False  # 结束当前块
            continue
        
        # 遇到新的段落起始标记时结束当前 block
        if in_block and (not line.strip() or line.startswith('【')):
            in_block = False
        
        # 如果当前行包含关键词，开始一个新的 block
        if any(k in line for k in keywords):
            # 对于短线趋势，再次检查是否包含超短线
            if is_short_trend and "超短线" in line:
                continue  # 跳过包含超短线的行
            in_block = True
            filtered.append(line)
            continue

        # 如果处于 block 中，则继续收集行
        if in_block:
            # 对于短线趋势，检查是否包含超短线
            if is_short_trend and "超短线" in line:
                in_block = False  # 结束当前块
                continue
            filtered.append(line)

    # 如果过滤后的内容太少，退化为原文（但仍要排除超短线）
    if filtered:
        result = "\n".join(filtered)
        return result  # 不截断，保持完整信息
    else:
        # 如果没有匹配的关键词，返回原文（但排除超短线信息）
        if is_short_trend:
            # 对于短线趋势，过滤掉超短线信息后返回
            filtered_lines = [line for line in lines if "超短线" not in line]
            return "\n".join(filtered_lines)
        return raw_text  # 不截断，保持完整信息


def _build_dimension_prompt(dim_info: dict, stock1_code: str, stock1_name: str, data1: str,
                            stock2_code: str, stock2_name: str, data2: str) -> str:
    """构造单维度对比 Prompt。要求模型仅输出 RESULT 行，便于解析。"""
    # 避免f-string中的引号冲突，使用字符串拼接
    dimension_name = dim_info['key']

    # 消息面对比使用本地计算，不需要特殊的Prompt构建
    # 这个函数不应该被消息面对比调用，因为消息面对比在前面已经处理并continue了
    
    # 对行业趋势对比进行特殊处理
    if dimension_name == '行业趋势对比':
        # 从行业信息中提取行业名称
        import re
        industry1_name = "未知行业"
        industry2_name = "未知行业"
        
        # 增强的行业名称提取逻辑，尝试多种格式，包括QMT格式
        def extract_industry_name(data):
            patterns = [
                # 格式1: "电池 的技术指标分析："（QMT原始格式）
                r'^([^\s]+)\s+的技术指标分析：',
                # 格式2: "所属行业 电池 的技术指标分析："（修改后的QMT格式）
                r'所属行业\s+([^\s的]+)\s*的技术指标',
                # 格式3: "【所属行业技术指标】\n所属行业 电池 的技术指标分析"
                r'【所属行业技术指标】[^\n]*\n[^\n]*所属行业\s+([^\s的]+)',
                # 格式4: "行业板块 '电池' (代码: 881128) 的技术指标分析："（旧格式兼容）
                r"行业板块\s*'([^']+)'\s*\(代码:\s*\d+\)",
                # 格式5: "行业板块 '电池' 的技术指标分析："（旧格式兼容）
                r"行业板块\s*'([^']+)'\s*的技术指标",
                # 格式6: "行业板块 电池 (代码: 881128)"（无引号，旧格式兼容）
                r"行业板块\s+([^\s\(]+)\s*\(代码:",
                # 格式7: 简化匹配，只要包含板块和单引号（旧格式兼容）
                r"板块\s*'([^']+)'",
                # 格式8: 任何以单引号包围的内容，在"代码"之前（旧格式兼容）
                r"'([^']+)'\s*\(代码:",
            ]
            
            for pattern in patterns:
                match = re.search(pattern, data, re.MULTILINE)
                if match:
                    name = match.group(1).strip()
                    # 跳过数字代码（如881128）
                    if name.isdigit():
                        continue
                    if name and name != "":
                        return name
            return "未知行业"
        
        # 提取两个股票的行业名称
        industry1_name = extract_industry_name(data1)
        industry2_name = extract_industry_name(data2)
        
        # 添加调试信息
        print(f"    [行业名称提取] 股票1行业: '{industry1_name}'")
        print(f"    [行业名称提取] 股票2行业: '{industry2_name}'")
        
        # 在数据前添加明确的行业标识
        data1 = f"【所属行业：{industry1_name}】\n{data1}"
        data2 = f"【所属行业：{industry2_name}】\n{data2}"

    # 对基本面对比进行特殊处理
    if dimension_name == '基本面对比':
        prompt = f"""以下给出两只股票的基本面数据，请根据基本面指标综合判断哪只股票的基本面更有利于短中期以更大的概率及空间上涨。（注意需考量低估值的“价值股”和高估值的“成长股”的不同特点进行综合对比）

输出格式严格为两行：
RESULT: <基本面更优秀的股票代码或 tie>
REASON: <300 字以内理由，说明基本面对比的具体分析>

【股票1】代码:{stock1_code} 名称:{stock1_name}
{data1}

【股票2】代码:{stock2_code} 名称:{stock2_name}
{data2}
"""
    else:
        prompt = f"""请扮演一位经验丰富的股票分析师，精通趋势交易理念和方法，擅长基于以下给出两只股票在"{dimension_name}"维度的相关信息(一些必要的指标或信号已直接算好并给出)，判断出其中哪只更具趋势交易价值。你需要这样做：
        1. 评估每只股票的行情及信号,挑选出最能体现趋势是否形成、是否得到确认以及趋势强度大小的信号。
        2. 如果同一个股票的趋势信号中存在利多和利空的冲突信号，请说明你是如何权衡的。
        3. 如果两个股票有着同种趋势信号，请说明你是如何横向对比的。
        4. 综合上述分析，基于你对趋势交易的深厚理解，判断出哪只股票的未来趋势更看好（综合趋势形成胜率及盈亏比判断）
输出格式严格为两行：
RESULT: <赢家股票代码或 tie>
REASON: <详细说明判断依据，按照【关键趋势信号列举】: ... 【冲突权衡】(如有): ... 【横向对比】: 关键信号1对比、关键信号2对比等 【未来趋势判断依据】 ... 的格式来写明决策过程>

【股票1】代码:{stock1_code} 名称:{stock1_name}
{data1}

【股票2】代码:{stock2_code} 名称:{stock2_name}
{data2}
"""
    return prompt


def _parse_dimension_result(result_text: str, code1: str, code2: str):
    """解析 LLM 返回的单维度结果，返回 code1 / code2 / 'tie' / None"""
    import re
    m = re.search(r"RESULT[:：]\s*(\d{6}|tie)", result_text, re.IGNORECASE)
    if m:
        val = m.group(1)
        if val.lower() == "tie":
            return "tie"
        if val in (code1, code2):
            return val
    # 若格式不符，尝试简易判断
    if code1 in result_text and any(x in result_text for x in ["胜", "优", "强"]):
        return code1
    if code2 in result_text and any(x in result_text for x in ["胜", "优", "强"]):
        return code2
    return None


# ==================== 新增：多维度拆分主函数 compare_stocks_modular ====================

def compare_stocks_modular_industry(pair_index, stock1, stock2, stock_tech_info, client, code2name, output_queue,
                                   output_file_lock, output_file, original_headers, model_log_file=None,
                                   max_retries=3, retry_delay=5, industry_tech_info=None, stock_to_industry=None,
                                   stock_resonance_info=None, debug_mode=False, stock_item_scores_map=None, stock_extra_map=None,
                                   fundamental_cache=None, model_type=None, industry_name=None):
    """行业内股票对比专用函数，跳过行业趋势对比维度"""
    # 调用原函数，但在维度处理中跳过行业趋势对比
    return compare_stocks_modular(pair_index, stock1, stock2, stock_tech_info, client, code2name, output_queue,
                                output_file_lock, output_file, original_headers, model_log_file,
                                max_retries, retry_delay, industry_tech_info, stock_to_industry,
                                stock_resonance_info, debug_mode, stock_item_scores_map, stock_extra_map,
                                fundamental_cache, model_type, skip_industry_comparison=True, industry_name=industry_name)

def compare_stocks_modular(pair_index, stock1, stock2, stock_tech_info, client, code2name, output_queue,
                           output_file_lock, output_file, original_headers, model_log_file=None,
                           max_retries=3, retry_delay=5, industry_tech_info=None, stock_to_industry=None,
                           stock_resonance_info=None, debug_mode=False, stock_item_scores_map=None, stock_extra_map=None,
                           fundamental_cache=None, model_type=None, skip_industry_comparison=False, industry_name=None):
    """拆分为 8 维度分别调用 LLM 的股票对比。整体输出逻辑与旧版保持一致。"""
    import traceback, time, json, re

    stock1_code = stock1.get('代码', '未知') or '未知'
    stock1_name = stock1.get('名称', '未知') or '未知'
    stock2_code = stock2.get('代码', '未知') or '未知'
    stock2_name = stock2.get('名称', '未知') or '未知'

    print(f"[MOD] 对比第{pair_index+1}组: {stock1_code}({stock1_name}) vs {stock2_code}({stock2_name})")

    # ----- 准备各类原始信息 -----
    tech_info1 = stock_tech_info.get(stock1_code, "")
    tech_info2 = stock_tech_info.get(stock2_code, "")

    capital_info1 = get_historical_data(stock1_code, original_headers, current_stock=stock1)
    capital_info2 = get_historical_data(stock2_code, original_headers, current_stock=stock2)

    # 行业技术 & 共振信息
    industry_info1 = ""
    industry_info2 = ""
    resonance_info1 = ""
    resonance_info2 = ""
    if stock_to_industry:
        full_ind1 = stock_to_industry.get(stock1_code, "")
        full_ind2 = stock_to_industry.get(stock2_code, "")
        
        # 添加调试信息
        if full_ind1 or full_ind2:
            print(f"  [行业映射调试]:")
            print(f"    股票1({stock1_code}) -> 完整行业名: '{full_ind1}'")
            print(f"    股票2({stock2_code}) -> 完整行业名: '{full_ind2}'")
        
        ind1 = full_ind1.split('-')[-1] if '-' in full_ind1 else full_ind1
        ind2 = full_ind2.split('-')[-1] if '-' in full_ind2 else full_ind2
        
        # 添加更多调试信息
        if ind1 or ind2:
            print(f"    处理后行业名1: '{ind1}'")
            print(f"    处理后行业名2: '{ind2}'")
            print(f"    industry_tech_info 包含的行业: {list(industry_tech_info.keys())[:5]}... (前5个)")
        
        if ind1 and industry_tech_info and ind1 in industry_tech_info:
            industry_info1 = industry_tech_info[ind1]
            print(f"    成功获取股票1的行业信息，长度: {len(industry_info1)}")
        else:
            if ind1:
                print(f"    警告：行业 '{ind1}' 不在 industry_tech_info 中")
            
        if ind2 and industry_tech_info and ind2 in industry_tech_info:
            industry_info2 = industry_tech_info[ind2]
            print(f"    成功获取股票2的行业信息，长度: {len(industry_info2)}")
        else:
            if ind2:
                print(f"    警告：行业 '{ind2}' 不在 industry_tech_info 中")
    if stock_resonance_info:
        if stock1_code in stock_resonance_info:
            cnt = stock_resonance_info[stock1_code]['count']
            resonance_info1 = f"该股票所在行业有 {cnt} 支放量突破个股"
        if stock2_code in stock_resonance_info:
            cnt = stock_resonance_info[stock2_code]['count']
            resonance_info2 = f"该股票所在行业有 {cnt} 支放量突破个股"

    # 汇总信息用于关键词过滤
    raw_info1 = "\n".join([tech_info1, capital_info1, industry_info1, resonance_info1])
    raw_info2 = "\n".join([tech_info2, capital_info2, industry_info2, resonance_info2])

    # 记录各维度胜负
    item_results = {}
    # 新增：保存每个维度的详细处理结果，用于日志输出
    dimension_details = {}

    # ---------- 并行处理所有维度 ----------
    print(f"  开始并行处理 {len(DIMENSION_DEFINITIONS)} 个维度...")

    # 使用线程池并行处理维度
    with concurrent.futures.ThreadPoolExecutor(max_workers=8) as dim_executor:
        # 提交所有维度任务
        dimension_futures = {}
        for dim in DIMENSION_DEFINITIONS:
            # 如果是行业内对比且当前维度是行业趋势对比，则跳过
            if skip_industry_comparison and dim['key'] == '行业趋势及共振对比':
                # 为行业内对比设置默认平局结果
                item_results[dim['key']] = 'tie'
                dimension_details[dim['key']] = {
                    'prompt': f'行业内对比跳过行业趋势对比维度（行业：{industry_name}）',
                    'response': '同行业内对比，行业趋势对比维度默认为平局',
                    'processing_time': 0.0,
                    'retries_used': 0
                }
                continue

            future = dim_executor.submit(
                process_single_dimension, dim, stock1_code, stock1_name, stock2_code, stock2_name,
                tech_info1, tech_info2, capital_info1, capital_info2,
                industry_info1, industry_info2, resonance_info1, resonance_info2,
                client, fundamental_cache, stock_item_scores_map, stock_extra_map,
                stock_to_industry, industry_tech_info, model_type
            )
            dimension_futures[future] = dim['key']

        # 收集结果
        for future in concurrent.futures.as_completed(dimension_futures):
            dim_key = dimension_futures[future]
            try:
                result_dim_key, result, details = future.result()
                item_results[result_dim_key] = result
                dimension_details[result_dim_key] = details
                print(f"    完成维度 {result_dim_key}: {result}")

                # 添加调试信息
                if result_dim_key in ['盈亏比对比', '资金面对比']:
                    print(f"[CRITICAL_DEBUG] 存储维度详情: {result_dim_key}")
                    print(f"[CRITICAL_DEBUG] details keys: {list(details.keys())}")
                    if 'comparison_info' in details:
                        print(f"[CRITICAL_DEBUG] comparison_info keys: {list(details['comparison_info'].keys())}")
                        print(f"[CRITICAL_DEBUG] comparison_info 内容: {details['comparison_info']}")
                    else:
                        print(f"[CRITICAL_DEBUG] 没有找到 comparison_info")
            except Exception as e:
                print(f"    维度 {dim_key} 处理失败: {e}")
                item_results[dim_key] = None
                dimension_details[dim_key] = {
                    'prompt': '',
                    'response': '',
                    'error': str(e),
                    'processing_time': 0,
                    'retries_used': 0
                }

    print(f"  所有维度处理完成")

    # ---------- 统计比分 ----------
    # 如果是行业内对比，排除"行业趋势及共振对比"
    excluded_dimensions = []
    if skip_industry_comparison:
        excluded_dimensions.append('行业趋势及共振对比')

    filtered_results = {k: v for k, v in item_results.items() if k not in excluded_dimensions}
    stock1_wins = sum(1 for w in filtered_results.values() if w == stock1_code)
    stock2_wins = sum(1 for w in filtered_results.values() if w == stock2_code)
    ties = sum(1 for w in filtered_results.values() if w == 'tie')
    valid_items = sum(1 for w in filtered_results.values() if w is not None)

    if stock1_wins > stock2_wins:
        overall_winner = stock1_code
    elif stock2_wins > stock1_wins:
        overall_winner = stock2_code
    else:
        overall_winner = 'tie'

    score_text = f"{stock1_wins}:{stock2_wins} (平局:{ties})"

    # ---------- 写入输出文件 ----------
    with output_file_lock:
        with open(output_file, "a", encoding="utf-8") as fout:
            fout.write(f"第{pair_index+1}组对比\n")
            fout.write(f"对比股票: {stock1_code}({stock1_name}) vs {stock2_code}({stock2_name})\n")
            fout.write(f"{'='*80}\n\n")

            # 详细记录每个维度的处理过程
    print(f"  所有维度处理完成")



    if stock1_wins > stock2_wins:
        overall_winner = stock1_code
    elif stock2_wins > stock1_wins:
        overall_winner = stock2_code
    else:
        overall_winner = 'tie'

    score_text = f"{stock1_wins}:{stock2_wins} (平局:{ties})"

    # ---------- 写入输出文件 ----------
    with output_file_lock:
        with open(output_file, "a", encoding="utf-8") as fout:
            fout.write(f"第{pair_index+1}组对比\n")
            fout.write(f"对比股票: {stock1_code}({stock1_name}) vs {stock2_code}({stock2_name})\n")
            fout.write(f"{'='*80}\n\n")
            
            # 详细记录每个维度的处理过程
            for idx, dim in enumerate(DIMENSION_DEFINITIONS):
                dim_key = dim['key']
                winner = item_results.get(dim_key, 'tie')

                # 已移除"超短趋势对比"维度，无需跳过

                # 如果是行业内对比且当前维度是行业趋势对比，则特殊处理
                if skip_industry_comparison and dim_key == '行业趋势及共振对比':
                    fout.write(f"【维度 {idx}：{dim_key}】\n")
                    fout.write(f"{'-'*60}\n")
                    fout.write(f"【行业内对比模式】\n")
                    fout.write(f"行业内对比跳过行业趋势对比维度（行业：{industry_name}）\n")
                    fout.write(f"同行业内股票的行业趋势相同，该维度默认为平局\n")
                    fout.write(f"【最终判定】{dim_key}: 平局\n")
                    fout.write(f"{'='*60}\n\n")
                    continue
                
                # 维度编号
                display_idx = idx
                fout.write(f"【维度 {display_idx}：{dim_key}】\n")
                fout.write(f"{'-'*60}\n")

                # 添加调试信息
                print(f"[CRITICAL_DEBUG] 输出格式化: 当前维度={dim_key}")
                print(f"[CRITICAL_DEBUG] dimension_details 所有键: {list(dimension_details.keys())}")

                # 写入调试文件
                with open("debug_output.txt", "a", encoding="utf-8") as debug_file:
                    debug_file.write(f"[CRITICAL_DEBUG] 输出格式化: 当前维度={dim_key}\n")
                    debug_file.write(f"[CRITICAL_DEBUG] dimension_details 所有键: {list(dimension_details.keys())}\n")

                if dim_key == '盈亏比对比':
                    # 使用新的盈亏比对比逻辑进行详细对比
                    fout.write(f"【盈亏比对比 - 新逻辑】\n")

                    # 获取对比信息
                    details = dimension_details.get(dim_key, {})
                    comparison_info = details.get('comparison_info', {})
                    print(f"[CRITICAL_DEBUG] 盈亏比对比输出格式化: dim_key={dim_key}")
                    print(f"[CRITICAL_DEBUG] dimension_details keys: {list(dimension_details.keys())}")
                    print(f"[CRITICAL_DEBUG] details keys: {list(details.keys())}")
                    print(f"[CRITICAL_DEBUG] comparison_info keys: {list(comparison_info.keys())}")
                    print(f"[CRITICAL_DEBUG] comparison_info 内容: {comparison_info}")

                    # 写入调试文件
                    with open("debug_output.txt", "a", encoding="utf-8") as debug_file:
                        debug_file.write(f"[CRITICAL_DEBUG] 盈亏比对比输出格式化: dim_key={dim_key}\n")
                        debug_file.write(f"[CRITICAL_DEBUG] dimension_details keys: {list(dimension_details.keys())}\n")
                        debug_file.write(f"[CRITICAL_DEBUG] details keys: {list(details.keys())}\n")
                        debug_file.write(f"[CRITICAL_DEBUG] comparison_info keys: {list(comparison_info.keys())}\n")
                        debug_file.write(f"[CRITICAL_DEBUG] comparison_info 内容: {comparison_info}\n")

                    fout.write(f"对比方法: {comparison_info.get('method', 'unknown')}\n")
                    fout.write(f"对比情况: {comparison_info.get('case', 'unknown')}\n")
                    fout.write(f"对比结果: {comparison_info.get('reason', 'unknown')}\n")

                    # 显示股票1信息
                    stock1_info = comparison_info.get('stock1_info', {})
                    if stock1_info:
                        fout.write(f"\n股票 {stock1_code}({stock1_name}) 详情:\n")
                        fout.write(f"  当前价格: {stock1_info.get('current_price', 'N/A')}\n")
                        fout.write(f"  中枢上沿: {stock1_info.get('zs_upper', 'N/A')}\n")
                        fout.write(f"  位于中枢上方: {stock1_info.get('above_zs', 'N/A')}\n")

                    # 显示股票2信息
                    stock2_info = comparison_info.get('stock2_info', {})
                    if stock2_info:
                        fout.write(f"\n股票 {stock2_code}({stock2_name}) 详情:\n")
                        fout.write(f"  当前价格: {stock2_info.get('current_price', 'N/A')}\n")
                        fout.write(f"  中枢上沿: {stock2_info.get('zs_upper', 'N/A')}\n")
                        fout.write(f"  位于中枢上方: {stock2_info.get('above_zs', 'N/A')}\n")

                    # 根据不同情况显示额外信息
                    case = comparison_info.get('case', '')
                    if case == 'both_below_zs':
                        fout.write(f"\n【均在中枢下方情况】\n")
                        fout.write(f"股票1盈亏比: {comparison_info.get('ratio1', 'N/A')}\n")
                        fout.write(f"股票2盈亏比: {comparison_info.get('ratio2', 'N/A')}\n")
                    elif case == 'both_above_zs':
                        fout.write(f"\n【均在中枢上方情况】\n")
                        fout.write(f"股票1中枢能量密度: {comparison_info.get('energy_density1', 'N/A')}\n")
                        fout.write(f"股票2中枢能量密度: {comparison_info.get('energy_density2', 'N/A')}\n")

                    fout.write(f"\n【最终判定】{dim_key}: {result}\n")

                elif dim_key == '基本面对比':
                    # 基本面对比的详细过程
                    try:
                        # 获取基本面数据
                        fundamental_data1 = get_fundamental_data_for_comparison(stock1_code)
                        fundamental_data2 = get_fundamental_data_for_comparison(stock2_code)

                        fout.write(f"【LLM 基本面对比模式】\n")
                        fout.write(f"股票 {stock1_code}({stock1_name}) 基本面数据:\n")
                        fout.write(f"{fundamental_data1}\n\n")

                        fout.write(f"股票 {stock2_code}({stock2_name}) 基本面数据:\n")
                        fout.write(f"{fundamental_data2}\n\n")

                        # 从保存的详情中获取LLM处理结果
                        details = dimension_details.get(dim_key, {})
                        prompt = details.get('prompt', '未保存 Prompt')
                        model_response = details.get('response', '未保存响应')
                        error_info = details.get('error', None)
                        processing_time = details.get('processing_time', 0)

                        fout.write(f"处理耗时: {processing_time:.2f}秒\n")
                        if error_info:
                            fout.write(f"错误信息: {error_info}\n")
                        fout.write(f"\n")

                        fout.write(f"【LLM Prompt】\n")
                        fout.write(f"{prompt}\n")
                        fout.write(f"{'-'*40}\n")

                        fout.write(f"【LLM 响应】\n")
                        fout.write(f"{model_response}\n")
                        fout.write(f"{'-'*40}\n")

                        # 解析结果
                        if model_response:
                            parsed_winner = _parse_dimension_result(model_response, stock1_code, stock2_code)
                            if parsed_winner == stock1_code:
                                fout.write(f"【对比结果】{stock1_code}({stock1_name}) 基本面更优秀\n")
                            elif parsed_winner == stock2_code:
                                fout.write(f"【对比结果】{stock2_code}({stock2_name}) 基本面更优秀\n")
                            elif parsed_winner == 'tie':
                                fout.write(f"【对比结果】两股票基本面相当，判定为平局\n")
                            else:
                                fout.write(f"【对比结果】解析失败，默认为平局\n")
                        else:
                            fout.write(f"【对比结果】LLM响应为空，默认为平局\n")

                    except Exception as e:
                        fout.write(f"【基本面对比失败】{e}\n")
                        fout.write(f"【对比结果】对比失败，默认为平局\n")

                elif dim_key == '资金面对比':
                    # 使用新的资金面对比逻辑进行详细对比
                    fout.write(f"【资金面对比 - 新逻辑】\n")

                    # 获取对比信息
                    details = dimension_details.get(dim_key, {})
                    comparison_info = details.get('comparison_info', {})
                    print(f"[CRITICAL_DEBUG] 资金面对比输出格式化: dim_key={dim_key}")
                    print(f"[CRITICAL_DEBUG] dimension_details keys: {list(dimension_details.keys())}")
                    print(f"[CRITICAL_DEBUG] details keys: {list(details.keys())}")
                    print(f"[CRITICAL_DEBUG] comparison_info keys: {list(comparison_info.keys())}")
                    print(f"[CRITICAL_DEBUG] comparison_info 内容: {comparison_info}")

                    fout.write(f"对比方法: {comparison_info.get('method', 'unknown')}\n")
                    fout.write(f"对比结果: {comparison_info.get('reason', 'unknown')}\n\n")

                    # 显示股票1详情
                    stock1_info = comparison_info.get('stock1_info', {})
                    if stock1_info:
                        fout.write(f"股票 {stock1_code}({stock1_name}) 详情:\n")
                        fout.write(f"  资金活跃度得分: {stock1_info.get('activity_score', 'N/A')}分\n")
                        fout.write(f"  主力净流入得分: {stock1_info.get('flow_score', 'N/A')}分\n")
                        fout.write(f"  子维度总分: {stock1_info.get('total_score', 'N/A')}分\n")
                        fout.write(f"  最终得分: {stock1_info.get('final_score', 'N/A')}分\n")

                        # 显示详细的资金活跃度分析
                        activity_details = stock1_info.get('details', {}).get('activity_details', {})
                        if activity_details and not activity_details.get('error'):
                            fout.write(f"  资金活跃度详情:\n")
                            fout.write(f"    中枢内日均成交量: {activity_details.get('zs_avg_volume', 'N/A')}\n")
                            fout.write(f"    中枢内周峰值: {activity_details.get('zs_weekly_volume', 'N/A')}\n")
                            fout.write(f"    中枢后日峰值: {activity_details.get('after_zs_max_volume', 'N/A')}\n")
                            fout.write(f"    中枢后周峰值: {activity_details.get('after_zs_weekly_volume', 'N/A')}\n")
                        fout.write(f"\n")

                    # 显示股票2详情
                    stock2_info = comparison_info.get('stock2_info', {})
                    if stock2_info:
                        fout.write(f"股票 {stock2_code}({stock2_name}) 详情:\n")
                        fout.write(f"  资金活跃度得分: {stock2_info.get('activity_score', 'N/A')}分\n")
                        fout.write(f"  主力净流入得分: {stock2_info.get('flow_score', 'N/A')}分\n")
                        fout.write(f"  子维度总分: {stock2_info.get('total_score', 'N/A')}分\n")
                        fout.write(f"  最终得分: {stock2_info.get('final_score', 'N/A')}分\n")

                        # 显示详细的资金活跃度分析
                        activity_details = stock2_info.get('details', {}).get('activity_details', {})
                        if activity_details and not activity_details.get('error'):
                            fout.write(f"  资金活跃度详情:\n")
                            fout.write(f"    中枢内日均成交量: {activity_details.get('zs_avg_volume', 'N/A')}\n")
                            fout.write(f"    中枢内周峰值: {activity_details.get('zs_weekly_volume', 'N/A')}\n")
                            fout.write(f"    中枢后日峰值: {activity_details.get('after_zs_max_volume', 'N/A')}\n")
                            fout.write(f"    中枢后周峰值: {activity_details.get('after_zs_weekly_volume', 'N/A')}\n")
                        fout.write(f"\n")

                    fout.write(f"【最终判定】{dim_key}: {result}\n")
                        
                elif dim_key == '消息面对比':
                    # 消息面对比：使用已获取的消息面信息进行本地对比（复刻模式6）
                    try:
                        # 从dimension_details中获取已计算的结果
                        details = dimension_details.get(dim_key, {})
                        response_text = details.get('response', '')

                        # 从news_prompt_log.txt中提取新闻信息用于显示
                        news_titles1 = []
                        news_titles2 = []

                        try:
                            if os.path.exists("news_prompt_log.txt"):
                                with open("news_prompt_log.txt", "r", encoding="utf-8") as news_log:
                                    content = news_log.read()
                                    import re

                                    # 提取股票1的新闻
                                    stock1_section = re.search(f"股票: {stock1_code}\\({stock1_name}\\)[\\s\\S]*?【已获取的新闻信息】\\s*([\\s\\S]*?)(?:基于我给你的|$)", content)
                                    if stock1_section:
                                        news_text = stock1_section.group(1).strip()
                                        news_titles1 = [line.strip() for line in news_text.split('\n') if line.strip() and not line.startswith('akshare')]

                                    # 提取股票2的新闻
                                    stock2_section = re.search(f"股票: {stock2_code}\\({stock2_name}\\)[\\s\\S]*?【已获取的新闻信息】\\s*([\\s\\S]*?)(?:基于我给你的|$)", content)
                                    if stock2_section:
                                        news_text = stock2_section.group(1).strip()
                                        news_titles2 = [line.strip() for line in news_text.split('\n') if line.strip() and not line.startswith('akshare')]
                        except Exception as e:
                            print(f"提取新闻标题失败: {e}")

                        # 写入日志
                        fout.write(f"【本地计算模式（大模型搜索新闻）】\n")
                        fout.write(f"关键词过滤: {dim['keywords']}\n")

                        # 显示新闻标题列表
                        fout.write(f"\n【股票1】{stock1_code}({stock1_name}) 新闻标题列表:\n")
                        if news_titles1:
                            for i, title in enumerate(news_titles1[:10]):  # 最多显示10条
                                fout.write(f"  {i+1}. {title}\n")
                        else:
                            fout.write(f"  未找到相关新闻\n")

                        fout.write(f"\n【股票2】{stock2_code}({stock2_name}) 新闻标题列表:\n")
                        if news_titles2:
                            for i, title in enumerate(news_titles2[:10]):  # 最多显示10条
                                fout.write(f"  {i+1}. {title}\n")
                        else:
                            fout.write(f"  未找到相关新闻\n")

                        fout.write(f"\n处理耗时: {details.get('processing_time', 0):.2f}秒\n")
                        fout.write(f"重试次数: {details.get('retries_used', 0)}\n\n")

                        # 不再写入错误的对比Prompt，而是说明使用本地计算
                        fout.write(f"【处理方式】\n")
                        fout.write(f"本地计算模式：为每只股票单独获取消息面信息，然后进行本地评分对比\n")
                        fout.write(f"详细的单股票消息面分析请查看: news_prompt_log.txt\n")
                        fout.write(f"{'-'*40}\n\n")

                        fout.write(f"【本地对比结果】\n")
                        if response_text:
                            fout.write(f"{response_text}\n")
                        else:
                            fout.write("未获取到消息面对比结果\n")
                        fout.write(f"{'-'*40}\n\n")

                        fout.write(f"【解析过程】\n")
                        fout.write(f"使用本地计算模式，直接比较两只股票的消息面评分\n")
                        fout.write(f"最终解析结果: {winner}\n")

                    except Exception as e:
                        fout.write(f"【本地计算失败】{e}\n")
                        fout.write(f"【对比结果】计算失败，默认为平局\n")
                        
                elif dim_key == '中线趋势判断':
                    # 中线趋势判断：只使用中线趋势对比，不再合并超短趋势对比
                    # 从保存的详情中获取真实数据
                    details = dimension_details.get(dim_key, {})
                    prompt = details.get('prompt', '未保存 Prompt')
                    model_response = details.get('response', '未保存响应')
                    error_info = details.get('error', None)
                    processing_time = details.get('processing_time', 0)
                    retries_used = details.get('retries_used', 0)

                    fout.write(f"【LLM 请求模式】\n")
                    fout.write(f"关键词过滤: {dim['keywords']}\n")
                    fout.write(f"处理耗时: {processing_time:.2f}秒\n")
                    fout.write(f"重试次数: {retries_used}\n")
                    if error_info:
                        fout.write(f"错误信息: {error_info}\n")
                    fout.write(f"\n")

                    fout.write(f"【LLM Prompt】\n")
                    fout.write(f"{prompt}\n")
                    fout.write(f"{'-'*40}\n")

                    fout.write(f"【LLM 响应】\n")
                    fout.write(f"{model_response}\n")
                    fout.write(f"{'-'*40}\n")

                    # 解析结果
                    if model_response:
                        parsed_winner = _parse_dimension_result(model_response, stock1_code, stock2_code)
                        if parsed_winner == stock1_code:
                            fout.write(f"【对比结果】{stock1_code}({stock1_name}) 中线趋势更优秀\n")
                        elif parsed_winner == stock2_code:
                            fout.write(f"【对比结果】{stock2_code}({stock2_name}) 中线趋势更优秀\n")
                        elif parsed_winner == 'tie':
                            fout.write(f"【对比结果】两股票中线趋势相当，判定为平局\n")
                        else:
                            fout.write(f"【对比结果】解析失败，默认为平局\n")
                    else:
                        fout.write(f"【对比结果】LLM响应为空，默认为平局\n")
                        
                else:
                    # LLM 调用的详细过程
                    # 从保存的详情中获取真实数据
                    details = dimension_details.get(dim_key, {})
                    prompt = details.get('prompt', '未保存 Prompt')
                    model_response = details.get('response', '未保存响应')
                    error_info = details.get('error', None)
                    processing_time = details.get('processing_time', 0)
                    retries_used = details.get('retries_used', 0)
                    
                    fout.write(f"【LLM 请求模式】\n")
                    fout.write(f"关键词过滤: {dim['keywords']}\n")
                    fout.write(f"处理耗时: {processing_time:.2f}秒\n")
                    fout.write(f"重试次数: {retries_used}\n")
                    if error_info:
                        fout.write(f"错误信息: {error_info}\n")
                    fout.write(f"\n")
                    
                    fout.write(f"【发送给大模型的 Prompt】\n")
                    fout.write(f"{prompt}\n")
                    fout.write(f"{'-'*40}\n\n")
                    
                    fout.write(f"【大模型返回结果】\n")
                    if model_response:
                        fout.write(f"{model_response}\n")
                    else:
                        fout.write("(无响应或响应为空)\n")
                    fout.write(f"{'-'*40}\n\n")
                    
                    fout.write(f"【解析过程】\n")
                    if model_response:
                        # 显示解析过程
                        parsed_winner = _parse_dimension_result(model_response, stock1_code, stock2_code)
                        fout.write(f"尝试解析 RESULT 行...\n")
                        
                        import re
                        result_match = re.search(r"RESULT[:：]\s*(\d{6}|tie)", model_response, re.IGNORECASE)
                        if result_match:
                            result_value = result_match.group(1)
                            fout.write(f"找到 RESULT 行: {result_value}\n")
                            if result_value.lower() == "tie":
                                fout.write(f"解析为: 平局\n")
                            elif result_value in [stock1_code, stock2_code]:
                                winner_name = stock1_name if result_value == stock1_code else stock2_name
                                fout.write(f"解析为: {result_value}({winner_name}) 胜出\n")
                            else:
                                fout.write(f"股票代码不匹配，回退解析...\n")
                        else:
                            fout.write(f"未找到标准 RESULT 行，尝试回退解析...\n")
                            if stock1_code in model_response and any(x in model_response for x in ["胜", "优", "强"]):
                                fout.write(f"通过关键词匹配，判断 {stock1_code} 胜出\n")
                            elif stock2_code in model_response and any(x in model_response for x in ["胜", "优", "强"]):
                                fout.write(f"通过关键词匹配，判断 {stock2_code} 胜出\n")
                            else:
                                fout.write(f"无法解析，默认为平局\n")
                    else:
                        fout.write(f"由于响应为空，默认为平局\n")
                    
                    # 最终解析结果
                    if winner == stock1_code:
                        fout.write(f"最终解析结果: {stock1_code}({stock1_name}) 胜出\n")
                    elif winner == stock2_code:
                        fout.write(f"最终解析结果: {stock2_code}({stock2_name}) 胜出\n")
                    elif winner == 'tie':
                        fout.write(f"最终解析结果: 平局\n")
                    else:
                        fout.write(f"最终解析结果: 无效结果，默认为平局\n")
                
                fout.write(f"【最终判定】{dim_key}: {winner}\n")
                fout.write(f"{'='*60}\n\n")
            
            # 汇总结果
            dimension_count = 7 if not skip_industry_comparison else 6  # 行业内对比时减少1个维度
            comparison_type = "行业内对比" if skip_industry_comparison else "跨行业对比"
            fout.write(f"【{dimension_count}个维度汇总结果】（{comparison_type}）\n")
            if skip_industry_comparison:
                fout.write(f"注：行业内对比跳过\"行业趋势及共振对比\"维度\n")
            fout.write(f"{'-'*60}\n")
            display_count = 0
            for idx, dim in enumerate(DIMENSION_DEFINITIONS):
                dim_key = dim['key']

                # 如果是行业内对比且当前维度是行业趋势对比，则跳过汇总显示
                if skip_industry_comparison and dim_key == '行业趋势及共振对比':
                    continue
                
                winner = item_results.get(dim_key, 'tie')
                if winner == stock1_code:
                    winner_display = f"{stock1_code}({stock1_name})"
                elif winner == stock2_code:
                    winner_display = f"{stock2_code}({stock2_name})"
                elif winner == 'tie':
                    winner_display = "平局"
                else:
                    winner_display = "无效"
                fout.write(f"{display_count + 1}. {dim_key}: {winner_display}\n")
                display_count += 1
            
            fout.write(f"\n【最终比分统计】\n")
            fout.write(f"{stock1_code}({stock1_name}) 胜出维度: {stock1_wins} 个\n")
            fout.write(f"{stock2_code}({stock2_name}) 胜出维度: {stock2_wins} 个\n")
            fout.write(f"平局维度: {ties} 个\n")
            fout.write(f"有效对比维度: {valid_items} 个\n")
            fout.write(f"比分: {score_text}\n")
            
            if overall_winner == 'tie':
                fout.write(f"【最终结果】平局\n\n")
            elif overall_winner:
                win_name = stock1_name if overall_winner == stock1_code else stock2_name
                fout.write(f"【最终结果】{overall_winner}({win_name}) 胜出\n\n")
            else:
                fout.write(f"【最终结果】无有效胜出者\n\n")
            
            fout.write(f"{'='*80}\n\n")

    # ---------- 推送到结果队列 ----------
    output_queue.put((pair_index, overall_winner, stock1_code, stock1_name, stock2_code, stock2_name, score_text, json.dumps(filtered_results, ensure_ascii=False), ties))
    return overall_winner, score_text, pair_index

# ====================== 新增部分结束 =====================

class LLMSearchClient:
    """火山引擎deepseek r1联网搜索版客户端，用于获取股票消息面信息"""
    
    def __init__(self):
        # 火山引擎API配置
        self.api_url = "https://ark.cn-beijing.volces.com/api/v3/bots/chat/completions"
        self.api_key = "e1283034-c29c-41bc-a10f-14e32fbe828b"  # 从llm_search.py获取
        self.model = "bot-20250626214818-m2bzp"  # deepseek r1联网搜索版模型ID
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        self.connected = False
    
    def connect(self):
        """连接到服务"""
        if not self.api_key:
            print("API密钥不能为空")
            self.connected = False
            return False
        
        # 测试连接
        try:
            test_payload = {
                "model": self.model,
                "stream": False,
                "messages": [
                    {
                        "role": "user",
                        "content": "你好"
                    }
                ]
            }
            
            response = requests.post(
                self.api_url, 
                headers=self.headers, 
                json=test_payload,
                timeout=30
            )
            
            if response.status_code == 200:
                self.connected = True
                print("成功连接到火山引擎deepseek r1联网搜索版")
                return True
            else:
                print(f"连接测试失败: {response.status_code}")
                self.connected = False
                return False
                
        except Exception as e:
            print(f"连接测试失败: {e}")
            self.connected = False
            return False
    
    def send(self, content):
        """发送消息到LLM并获取回复（优化超时和错误处理）"""
        if not self.connected:
            return ""
        
        try:
            payload = {
                "model": self.model,
                "stream": False,
                "messages": [
                    {
                        "role": "user",
                        "content": content
                    }
                ]
            }
            
            # 联网搜索需要更长的超时时间：连接30秒，读取180秒（3分钟）
            response = requests.post(
                self.api_url, 
                headers=self.headers, 
                json=payload,
                timeout=(30, 180)
            )
            
            if response.status_code != 200:
                error_msg = f"API错误: {response.status_code}"
                try:
                    error_detail = response.json()
                    if "error" in error_detail:
                        error_msg += f" - {error_detail['error']}"
                except:
                    error_msg += f" - {response.text[:200]}"
                print(error_msg)
                return ""
            
            result = response.json()
            
            # 获取回复内容
            if "choices" in result and len(result["choices"]) > 0:
                choice = result["choices"][0]
                if "message" in choice and "content" in choice["message"]:
                    content_result = choice["message"]["content"]
                    if content_result:
                        return content_result
                    else:
                        print("API返回内容为空")
                        return ""
            
            print("API响应格式异常")
            return ""
        
        except requests.exceptions.Timeout as e:
            print(f"请求超时: {e}")
            return ""
        except requests.exceptions.ConnectionError as e:
            print(f"连接错误: {e}")
            return ""
        except Exception as e:
            print(f"发送请求失败: {e}")
            return ""
    
    def close(self):
        """关闭连接"""
        self.connected = False


def test_fundamental_comparison():
    """测试基本面对比功能"""
    print("测试基本面数据获取功能...")

    # 测试股票代码
    test_codes = ["000001", "000002"]

    for code in test_codes:
        print(f"\n测试股票 {code}:")
        try:
            fundamental_data = get_fundamental_data_for_comparison(code)
            print(f"基本面数据获取成功，长度: {len(fundamental_data)}")
            print(f"前500字符: {fundamental_data[:500]}")
        except Exception as e:
            print(f"基本面数据获取失败: {e}")

    print("\n基本面数据获取测试完成")


if __name__ == "__main__":
    try:
        # 如果需要测试基本面功能，取消下面的注释
        # test_fundamental_comparison()
        main()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
    finally:
        print("程序已退出")
