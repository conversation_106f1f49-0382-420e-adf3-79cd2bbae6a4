# -*- coding: gbk -*-
import pandas as pd

import sys
import os
import locale
from datetime import datetime
import time
from typing import List, Tuple, Optional, Dict, Any
import logging

import warnings
warnings.filterwarnings('ignore')


# 添加缠论库路径
try:
    current_dir = os.path.dirname(os.path.abspath(__file__))
except NameError:
    current_dir = os.getcwd()
chan_path = os.path.join(current_dir, 'libs', 'chan')
if chan_path not in sys.path:
    sys.path.insert(0, chan_path)

# 配置日志系统
LOG_DIR = r"d:\stock"
LOG_FILE = os.path.join(LOG_DIR, "3buy_log.txt")

def setup_logging():
    """设置日志系统"""
    # 确保日志目录存在
    if not os.path.exists(LOG_DIR):
        os.makedirs(LOG_DIR, exist_ok=True)
    
    # 配置日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S',
        handlers=[
            # 文件处理器 - 覆盖模式
            logging.FileHandler(LOG_FILE, mode='w', encoding='gbk'),
            # 控制台处理器
            logging.StreamHandler(sys.stdout)
        ]
    )

    logger = logging.getLogger()
    logger.info("=" * 80)
    logger.info("缠论3买点检测程序启动")
    logger.info(f"日志文件: {LOG_FILE}")
    logger.info("=" * 80)
    return logger

# 导入缠论相关模块
CHAN_AVAILABLE = False
try:
    from Chan import CChan
    from ChanConfig import CChanConfig
    from Common.CEnum import AUTYPE, DATA_FIELD, KL_TYPE, DATA_SRC
    from Common.CTime import CTime
    from KLine.KLine_Unit import CKLine_Unit
    try:
        from DataAPI.CommonStockAPI import CCommonStockApi
    except ImportError:
        # 如果没有CCommonStockApi，创建一个简单的基类
        class CCommonStockApi:
            def __init__(self, code, k_type=None, begin_date=None, end_date=None, autype=None):
                self.code = code
                self.k_type = k_type
                self.begin_date = begin_date
                self.end_date = end_date
                self.autype = autype
    CHAN_AVAILABLE = True
except ImportError:
    CHAN_AVAILABLE = False



# 导入QMT相关函数
try:
    from Zstock_tech_qmttdx import connect_miniqmt, get_miniqmt_data
    import xtquant.xtdata as xtdata
except ImportError as e:
    print(f"QMT模块导入失败: {e}")
    print("请确保Zstock_tech_qmttdx.py文件存在")
    sys.exit(1)

# 通达信目录路径 - 参考stock_diff.py的定义
TDX_PATH = r"D:\new_tdx"
BLOCKNEW_DIR = os.path.join(TDX_PATH, "T0002", "blocknew")


def add_tdx_market_prefix(code: str) -> str:
    """
    为6位股票代码添加通达信市场前缀
    
    参数:
        code: 6位股票代码字符串
        
    返回:
        带市场前缀的股票代码
        
    规则:
        - 上海交易所(60、68开头)加前缀1
        - 深圳交易所(00、30、02开头)加前缀0
        - 其他格式保持原样
    """
    if isinstance(code, str) and len(code) == 6 and code.isdigit():
        # 上海交易所：主板600xxx，科创板688xxx
        if code.startswith(('60', '68')):
            return '1' + code
        # 深圳交易所：主板000xxx，中小板002xxx，创业板300xxx
        elif code.startswith(('00', '30', '02')):
            return '0' + code
    
    # 如果格式不符，返回原样
    return code


def filter_stock_codes(stock_codes):
    """过滤掉创业板等股票代码

    参考stock_diff.py的实现，支持处理带市场前缀的代码
    只保留6位数字且不以4/8/9开头的股票代码，或带前缀的对应格式
    """
    import re

    exclude_prefixes = {"4", "8", "9"}

    filtered_codes = []
    excluded_codes = []

    for raw_code in stock_codes:
        # 提取连续数字的第一个6~8位连续数字，一般情况下就是股票代码部分
        digits = re.findall(r"(\d{6,8})", raw_code)
        numeric_code = digits[-1] if digits else ""  # 取最后一个匹配

        # 处理带市场前缀的代码（7位）
        if len(numeric_code) == 7:
            # 去掉第一位市场前缀，取后6位进行判断
            actual_code = numeric_code[1:]
        elif len(numeric_code) >= 6:
            # 进一步提取末6位，通常板块文件的都是6位代码为准则
            actual_code = numeric_code[-6:]
        else:
            actual_code = numeric_code

        # 判断是否需要排除（基于实际的6位代码）
        if len(actual_code) == 6 and actual_code[0] in exclude_prefixes:
            excluded_codes.append(raw_code)
            continue

        filtered_codes.append(raw_code)

    if excluded_codes:
        logger.info(f"过滤掉{len(excluded_codes)}只创业板等股票: {', '.join(excluded_codes[:5])}{'...' if len(excluded_codes) > 5 else ''}")

    return filtered_codes


def write_block_file(stock_codes, block_file, append=False):
    """
    将股票代码写入通达信自定义板块文件

    参考stock_diff.py的实现，使用更健壮的文件读写方式

    参数:
        stock_codes: 股票代码列表
        block_file: 板块文件路径
        append: 是否追加模式，True为追加，False为覆盖

    返回:
        bool: 是否成功
    """
    try:
        # 确保目录存在
        dir_path = os.path.dirname(block_file)
        if not os.path.exists(dir_path):
            os.makedirs(dir_path, exist_ok=True)

        # 读取现有代码（使用更健壮的方式，参考stock_diff.py）
        existing_codes = []
        if os.path.exists(block_file) and append:
            try:
                # 使用二进制模式读取，然后解码（更健壮）
                with open(block_file, 'rb') as f:
                    content = f.read()
                
                # 解码文本
                text = content.decode('gbk', errors='ignore')
                
                # 分割行并去掉空行
                existing_codes = [line.strip() for line in text.replace('\r\n', '\n').split('\n') if line.strip()]
            except Exception as e:
                logger.error(f"读取现有代码失败: {e}")

        # 合并新旧代码
        existing_codes_set = set(existing_codes)
        new_codes = [code for code in stock_codes if code not in existing_codes_set]

        if append:
            # 新代码放在原有代码前面
            all_codes = new_codes + existing_codes
        else:
            # 覆盖模式，只使用新代码
            all_codes = stock_codes

        # 写入文件，使用GBK编码（完全模仿stock_diff.py的方式）
        with open(block_file, 'w', encoding='gbk') as f:
            for code in all_codes:
                f.write(f"{code}\n")

        # 输出详细日志
        file_basename = os.path.basename(block_file)
        if append:
            logger.info(f"写入{file_basename}: 原有{len(existing_codes)}只, 新增{len(new_codes)}只, 共{len(all_codes)}只")
        else:
            logger.info(f"覆盖写入{file_basename}: {len(stock_codes)}只")
        return True

    except Exception as e:
        logger.error(f"写入失败: {block_file} - {str(e)}")
        return False


class QMTDataAPI(CCommonStockApi):
    """
    自定义QMT数据接口，继承自CCommonStockApi
    """

    def __init__(self, code, k_type=KL_TYPE.K_DAY, begin_date=None, end_date=None, autype=AUTYPE.QFQ):
        super(QMTDataAPI, self).__init__(code, k_type, begin_date, end_date, autype)

    def get_kl_data(self):
        """
        从QMT获取K线数据并转换为缠论子项目所需格式
        """
        try:
            # 根据级别确定period参数
            period_map = {
                KL_TYPE.K_DAY: '1d',
                KL_TYPE.K_WEEK: '1w',
                KL_TYPE.K_60M: '60m',
                KL_TYPE.K_30M: '30m',
                KL_TYPE.K_15M: '15m',
                KL_TYPE.K_5M: '5m',
                KL_TYPE.K_1M: '1m'
            }

            period = period_map.get(self.k_type, '1d')

            # 特殊处理30分钟K线：根据QMT机制，需要先获取5分钟K线
            if self.k_type == KL_TYPE.K_30M:
                df_5m = get_miniqmt_data(self.code, period='5m', count=3000)  # 获取足够多的5分钟数据

                if df_5m.empty or 'error' in df_5m.columns:
                    df = get_miniqmt_data(self.code, period=period, count=500)
                else:
                    # 将5分钟数据转换为30分钟数据
                    df = self._convert_5min_to_30min(df_5m)
            else:
                # 其他级别直接获取
                df = get_miniqmt_data(self.code, period=period, count=500)

            if df.empty:
                return

            if 'error' in df.columns:
                return

            # 转换数据格式
            for _, row in df.iterrows():
                try:
                    # 解析时间
                    time_str = str(row['time'])
                    if '-' in time_str:  # 格式: 2024-01-01
                        parts = time_str.split(' ')[0].split('-')
                        year, month, day = int(parts[0]), int(parts[1]), int(parts[2])
                    else:  # 格式: 20240101
                        year = int(time_str[:4])
                        month = int(time_str[4:6])
                        day = int(time_str[6:8])

                    time_obj = CTime(year, month, day, 0, 0)

                    # 获取成交量数据
                    volume_raw = row.get('成交量', 0)
                    amount_raw = row.get('成交额', 0)

                    # 确保成交量数据不为空
                    if volume_raw is None:
                        volume_raw = 0
                    if amount_raw is None:
                        amount_raw = 0

                    # 创建K线数据
                    item_dict = {
                        DATA_FIELD.FIELD_TIME: time_obj,
                        DATA_FIELD.FIELD_OPEN: float(row['开盘']),
                        DATA_FIELD.FIELD_HIGH: float(row['最高']),
                        DATA_FIELD.FIELD_LOW: float(row['最低']),
                        DATA_FIELD.FIELD_CLOSE: float(row['收盘']),
                        DATA_FIELD.FIELD_VOLUME: float(volume_raw),
                        DATA_FIELD.FIELD_TURNOVER: float(amount_raw),
                        DATA_FIELD.FIELD_TURNRATE: float(row.get('换手率', 0))
                    }

                    yield CKLine_Unit(item_dict)

                except Exception:
                    continue

        except Exception:
            return

    def _convert_5min_to_30min(self, df_5m):
        """
        将5分钟K线数据转换为30分钟K线数据
        """
        try:
            if df_5m.empty:
                return pd.DataFrame()

            # 确保时间列是datetime类型
            if 'time' in df_5m.columns:
                df_5m['time'] = pd.to_datetime(df_5m['time'])
            elif 'date' in df_5m.columns:
                df_5m['date'] = pd.to_datetime(df_5m['date'])
                df_5m.rename(columns={'date': 'time'}, inplace=True)

            # 设置时间为索引
            df_5m.set_index('time', inplace=True)

            # 按30分钟重采样
            df_30m = df_5m.resample('30T').agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum',
                'amount': 'sum'
            }).dropna()

            # 重置索引，将时间列恢复
            df_30m.reset_index(inplace=True)

            return df_30m

        except Exception:
            return pd.DataFrame()

    @classmethod
    def do_init(cls):
        """初始化QMT连接"""
        return connect_miniqmt()

    @classmethod
    def do_close(cls):
        """关闭QMT连接"""
        pass


def clean_stock_code(code):
    """清理股票代码，去除非数字字符并确保6位格式"""
    if not code:
        return ""

    # 转为字符串
    code_str = str(code)

    # 去除所有非数字字符（如'='等）
    digits_only = ''.join(c for c in code_str if c.isdigit())

    # 确保6位格式，不足前面补0
    if digits_only:
        return digits_only.zfill(6)

    return ""


def find_name_column(columns):
    """查找股票名称列，支持'名称'和'名称(x)'格式"""
    # 首先检查是否有精确匹配'名称(x)'格式的列
    for col in columns:
        if isinstance(col, str) and col.startswith('名称(') and col.endswith(')'):
            return col

    # 其次检查是否有精确匹配'名称'的列
    for col in columns:
        if isinstance(col, str) and col == '名称':
            return col

    # 最后检查是否有以'名称'开头的列
    for col in columns:
        if isinstance(col, str) and col.startswith('名称'):
            return col

    return None


def read_tdx_xls(file_path: str) -> List[Tuple[str, str]]:
    """
    从TDX xls文件读取股票代码和名称
    返回: (代码, 名称) 元组列表
    """
    try:
        # 尝试不同的引擎和方法
        df = None

        # 方法1: 对于.xls文件尝试xlrd引擎
        if file_path.lower().endswith('.xls'):
            try:
                df = pd.read_excel(file_path, engine='xlrd')
            except Exception:
                pass

        # 方法2: 对于.xlsx文件尝试openpyxl引擎
        if df is None and file_path.lower().endswith('.xlsx'):
            try:
                df = pd.read_excel(file_path, engine='openpyxl')
            except Exception:
                pass

        # 方法3: 尝试默认pandas read_excel
        if df is None:
            try:
                df = pd.read_excel(file_path)
            except Exception:
                pass

        # 方法4: 尝试作为CSV读取（某些.xls文件实际是制表符分隔的）
        if df is None:
            try:
                df = pd.read_csv(file_path, sep='\t', encoding='gbk')
            except Exception:
                pass

        if df is None:
            raise Exception("所有读取方法都失败了")

        # 查找代码和名称列
        code_col = None
        name_col = None

        # 查找代码列
        for col in df.columns:
            col_str = str(col)
            col_lower = col_str.lower()
            if any(keyword in col_str for keyword in ['代码', 'code', '交易代码']) or any(keyword in col_lower for keyword in ['code', '股票代码']):
                code_col = col
                break

        # 查找名称列
        name_col = find_name_column(df.columns)

        if not code_col:
            raise Exception("未找到代码列")

        stock_list = []
        for _, row in df.iterrows():
            # 获取代码和名称
            code = row[code_col] if pd.notna(row[code_col]) else ""
            name = row[name_col] if name_col and pd.notna(row[name_col]) else ""

            # 如果名称是'nan'字符串则跳过
            if str(name).lower() == 'nan':
                continue

            # 清理Excel格式代码，如="000009"
            code_str = str(code)
            if code_str.startswith('="') and code_str.endswith('"'):
                code_str = code_str[2:-1]
            elif code_str.startswith('='):
                code_str = code_str[1:]

            # 移除引号和浮点转换的.0后缀
            code_str = code_str.strip('"').strip("'")
            if code_str.endswith('.0'):
                code_str = code_str[:-2]

            # 使用clean_stock_code函数清理代码
            cleaned_code = clean_stock_code(code_str)

            # 只处理6位数字代码
            if len(cleaned_code) == 6 and cleaned_code.isdigit():
                stock_list.append((cleaned_code, str(name)))

        return stock_list

    except Exception:
        return []


def get_chan_default_config():
    """
    获取ChanConfig.py的实际默认配置
    """
    try:
        # 创建一个空配置对象，获取默认值
        default_chan_config = CChanConfig({})

        # 提取关键默认配置参数
        default_config = {
            "bi_strict": default_chan_config.bi_conf.is_strict,
            "bi_fx_check": str(default_chan_config.bi_conf.bi_fx_check).split('.')[-1].lower(),  # 转换枚举为字符串
            "bi_end_is_peak": default_chan_config.bi_conf.bi_end_is_peak,
            "zs_algo": default_chan_config.zs_conf.zs_algo,
            "divergence_rate": default_chan_config.bs_point_conf.b_conf.divergence_rate,
            "min_zs_cnt": default_chan_config.bs_point_conf.b_conf.min_zs_cnt,
            "max_bs2_rate": default_chan_config.bs_point_conf.b_conf.max_bs2_rate,
        }

        return default_config
    except Exception as e:
        # 如果获取失败，使用硬编码的备用默认配置
        logger = logging.getLogger()
        logger.warning(f"无法获取ChanConfig默认配置，使用备用配置: {e}")
        return {
            "bi_strict": True,
            "bi_fx_check": "strict",
            "bi_end_is_peak": True,
            "zs_algo": "auto",
            "divergence_rate": 0.9,
            "min_zs_cnt": 1,
            "max_bs2_rate": 0.9999,
        }








def convert_5min_to_30min(df_5m):
    """
    将5分钟K线数据转换为30分钟K线数据
    """
    try:
        if df_5m.empty:
            return pd.DataFrame()

        # 统一列名映射
        column_mapping = {
            '日期': 'time',
            '时间': 'time',
            'date': 'time',
            '开盘': 'open',
            '最高': 'high',
            '最低': 'low',
            '收盘': 'close',
            '成交量': 'volume',
            '成交额': 'amount'
        }

        df_5m_renamed = df_5m.rename(columns=column_mapping)

        # 如果还没有time列，尝试其他可能的时间列名
        if 'time' not in df_5m_renamed.columns:
            for col in df_5m_renamed.columns:
                if any(keyword in str(col).lower() for keyword in ['time', 'date', '时间', '日期']):
                    df_5m_renamed.rename(columns={col: 'time'}, inplace=True)
                    break

        # 确保时间列是datetime类型
        if 'time' in df_5m_renamed.columns:
            df_5m_renamed['time'] = pd.to_datetime(df_5m_renamed['time'])
        else:
            return pd.DataFrame()

        # 设置时间为索引
        df_5m_copy = df_5m_renamed.copy()
        df_5m_copy.set_index('time', inplace=True)

        # 按30分钟重采样
        df_30m = df_5m_copy.resample('30min').agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum',
            'amount': 'sum'
        }).dropna()

        # 重置索引，将时间列恢复
        df_30m.reset_index(inplace=True)

        # 去除重复时间的行，保留最后一个
        df_30m = df_30m.drop_duplicates(subset=['time'], keep='last')

        # 确保时间是唯一且递增的
        df_30m = df_30m.sort_values('time').reset_index(drop=True)

        # 额外检查：确保没有重复时间戳
        duplicate_times = df_30m[df_30m.duplicated(subset=['time'], keep=False)]
        if not duplicate_times.empty:
            # 按时间分组，每组只保留最后一个
            df_30m = df_30m.groupby('time').last().reset_index()

        return df_30m

    except Exception:
        return pd.DataFrame()





def get_latest_zs_upper_price_with_configs(stock_code: str, csv_lines: List[str], k_type: KL_TYPE, debug_mode: bool = False, stock_name: str = "", df_raw: pd.DataFrame = None) -> Tuple[Optional[float], Optional[object], str, Optional[str], Optional[dict], Optional[str], Optional[object]]:
    """
    使用默认配置获取中枢上沿价格

    参数:
        stock_code: 股票代码
        csv_lines: CSV格式的K线数据
        k_type: K线级别
        debug_mode: 是否为调试模式（保留参数兼容性）
        stock_name: 股票名称（保留参数兼容性）
        df_raw: 原始K线数据（保留参数兼容性）

    返回:
        (上沿价格, 最新中枢对象, 信息描述, 配置名称, 中枢详细信息, 临时CSV文件路径, 缠论对象)
    """
    try:
        # 创建临时CSV文件目录
        chan_dir = os.path.join(os.path.dirname(__file__), 'libs', 'chan')
        if not os.path.exists(chan_dir):
            chan_dir = os.path.dirname(__file__)

        # 使用chan库期望的文件命名格式
        period_suffix = {
            KL_TYPE.K_DAY: 'day',
            KL_TYPE.K_WEEK: 'week',
            KL_TYPE.K_30M: '30m'
        }.get(k_type, 'day')

        temp_csv_file = os.path.join(chan_dir, f"{stock_code}_{period_suffix}.csv")

        # 写入临时CSV文件
        with open(temp_csv_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(csv_lines))

        # 使用默认配置
        config = CChanConfig({})

        # 创建缠论对象
        chan = CChan(
            code=stock_code,
            begin_time=None,
            end_time=None,
            data_src=DATA_SRC.CSV,
            lv_list=[k_type],
            config=config,
            autype=AUTYPE.QFQ
        )

        # 获取K线数据
        kl_data = chan[0]
        if not kl_data or len(kl_data.lst) == 0:
            # 清理临时CSV文件
            try:
                if os.path.exists(temp_csv_file):
                    os.remove(temp_csv_file)
            except:
                pass
            return None, None, "未找到K线数据", None, None, None, None

        # 获取中枢列表
        zs_list = kl_data.zs_list.zs_lst

        if not zs_list:
            # 清理临时CSV文件
            try:
                if os.path.exists(temp_csv_file):
                    os.remove(temp_csv_file)
            except:
                pass
            return None, None, "未找到中枢", None, None, None, None

        latest_zs = zs_list[-1]

        # 检查中枢的有效性
        is_valid_zs = True
        if hasattr(latest_zs, 'begin') and hasattr(latest_zs, 'end'):
            if hasattr(latest_zs.begin, 'time') and hasattr(latest_zs.end, 'time'):
                # 检查时间跨度是否合理
                if latest_zs.begin.time == latest_zs.end.time:
                    is_valid_zs = False

        if not is_valid_zs:
            # 清理临时CSV文件
            try:
                if os.path.exists(temp_csv_file):
                    os.remove(temp_csv_file)
            except:
                pass
            return None, None, "中枢无效", None, None, None, None

        # 计算中枢持续周期
        duration = 0
        if hasattr(latest_zs, 'begin') and hasattr(latest_zs, 'end') and hasattr(latest_zs.begin, 'idx') and hasattr(latest_zs.end, 'idx'):
            duration = latest_zs.end.idx - latest_zs.begin.idx + 1

        # 构建基本的中枢信息（不包含成交量信息）
        volume_info = {
            'start_time': latest_zs.begin.time if hasattr(latest_zs, 'begin') and hasattr(latest_zs.begin, 'time') else None,
            'end_time': latest_zs.end.time if hasattr(latest_zs, 'end') and hasattr(latest_zs.end, 'time') else None,
            'start_idx': latest_zs.begin.idx if hasattr(latest_zs, 'begin') and hasattr(latest_zs.begin, 'idx') else 0,
            'end_idx': latest_zs.end.idx if hasattr(latest_zs, 'end') and hasattr(latest_zs.end, 'idx') else 0
        }

        # 构建详细信息
        duration_info = f", 持续{duration}根K线"
        zs_info = f"默认配置, 上沿={latest_zs.high:.2f}, 下沿={latest_zs.low:.2f}, 中值={latest_zs.mid:.2f}{duration_info}"

        # 返回结果
        return latest_zs.high, latest_zs, zs_info, "默认配置", volume_info, temp_csv_file, chan

    except Exception as e:
        # 清理临时CSV文件
        try:
            if 'temp_csv_file' in locals() and temp_csv_file and os.path.exists(temp_csv_file):
                os.remove(temp_csv_file)
        except:
            pass
        return None, None, f"配置测试出错: {e}", None, None, None, None





def get_latest_zs_upper_price(chan_obj):
    """
    获取最新中枢的上沿价格（保留原函数用于兼容性）
    """
    try:
        # 获取第一级别的数据
        kl_data = chan_obj[0]

        # 获取中枢列表
        zs_list = kl_data.zs_list.zs_lst

        if not zs_list:
            return None, None, "未找到中枢"

        # 获取最后一个中枢
        latest_zs = zs_list[-1]

        # 返回中枢上沿价格和中枢信息
        return latest_zs.high, latest_zs, f"最新中枢: 下沿={latest_zs.low:.2f}, 上沿={latest_zs.high:.2f}, 中值={latest_zs.mid:.2f}"

    except Exception as e:
        return None, None, f"获取中枢信息出错: {e}"


def analyze_volume_in_zs_range(df_data: pd.DataFrame, zs_start_time, zs_end_time, logger) -> dict:
    """
    分析中枢范围内和中枢后的成交量情况

    参数:
        df_data: K线数据DataFrame，包含time, volume等列
        zs_start_time: 中枢开始时间
        zs_end_time: 中枢结束时间
        logger: 日志对象

    返回:
        包含成交量分析结果的字典
    """
    try:
        import pandas as pd
        from datetime import datetime, timedelta

        # 确保时间列是datetime类型
        if 'time' in df_data.columns:
            df_data = df_data.copy()
            df_data['time'] = pd.to_datetime(df_data['time'])
        else:
            logger.warning("数据中缺少time列，无法进行成交量分析")
            return {}

        # 确保有成交量列
        volume_col = None
        for col in ['volume', '成交量', 'Volume']:
            if col in df_data.columns:
                volume_col = col
                break

        if volume_col is None:
            logger.warning("数据中缺少成交量列，无法进行成交量分析")
            return {}

        # 转换中枢时间为datetime
        if zs_start_time and zs_end_time:
            if hasattr(zs_start_time, 'to_datetime'):
                zs_start_dt = zs_start_time.to_datetime()
                zs_end_dt = zs_end_time.to_datetime()
            else:
                zs_start_dt = pd.to_datetime(str(zs_start_time))
                zs_end_dt = pd.to_datetime(str(zs_end_time))
        else:
            logger.warning("中枢时间信息不完整，无法进行成交量分析")
            return {}

        # 1. 中枢范围内成交量分析
        zs_mask = (df_data['time'] >= zs_start_dt) & (df_data['time'] <= zs_end_dt)
        zs_data = df_data[zs_mask]

        if len(zs_data) == 0:
            logger.warning("中枢时间范围内没有找到K线数据")
            return {}

        # a. 中枢内日均成交量（股）
        zs_avg_volume = zs_data[volume_col].mean()

        # 2. 中枢结束后成交量分析
        after_zs_mask = df_data['time'] > zs_end_dt
        after_zs_data = df_data[after_zs_mask]

        # b. 中枢后日成交量峰值
        after_zs_max_volume = 0
        after_zs_max_date = None
        if len(after_zs_data) > 0:
            max_idx = after_zs_data[volume_col].idxmax()
            after_zs_max_volume = after_zs_data.loc[max_idx, volume_col]
            after_zs_max_date = after_zs_data.loc[max_idx, 'time'].strftime('%Y-%m-%d')

        # c. 中枢后峰值/中枢内均值比例
        volume_ratio = after_zs_max_volume / zs_avg_volume if zs_avg_volume > 0 else 0

        # 3. 周成交量分析
        # d. 中枢内最大周成交量
        zs_weekly_volume = 0
        zs_weekly_period = ""

        if len(zs_data) > 0:
            # 按周分组（周一到周日）
            zs_data_copy = zs_data.copy()
            zs_data_copy['week_start'] = zs_data_copy['time'].dt.to_period('W').dt.start_time
            zs_data_copy['week_end'] = zs_data_copy['week_start'] + timedelta(days=6)

            # 只统计完全包含在中枢范围内的周
            weekly_groups = zs_data_copy.groupby('week_start')
            max_weekly_volume = 0
            max_week_start = None
            max_week_end = None

            for week_start, week_data in weekly_groups:
                week_end = week_start + timedelta(days=6)
                # 检查这一周是否完全在中枢范围内
                if week_start >= zs_start_dt and week_end <= zs_end_dt:
                    week_volume = week_data[volume_col].sum()
                    if week_volume > max_weekly_volume:
                        max_weekly_volume = week_volume
                        max_week_start = week_start
                        max_week_end = week_end

            if max_week_start:
                zs_weekly_volume = max_weekly_volume
                zs_weekly_period = f"{max_week_start.strftime('%Y-%m-%d')} 至 {max_week_end.strftime('%Y-%m-%d')}"

        # e. 中枢后最大周成交量
        after_zs_weekly_volume = 0
        after_zs_weekly_period = ""

        if len(after_zs_data) > 0:
            # 包含中枢结束后的所有数据进行周分组
            all_after_data = df_data[df_data['time'] > zs_end_dt].copy()
            if len(all_after_data) > 0:
                all_after_data['week_start'] = all_after_data['time'].dt.to_period('W').dt.start_time

                weekly_groups = all_after_data.groupby('week_start')
                max_weekly_volume = 0
                max_week_start = None

                for week_start, week_data in weekly_groups:
                    week_volume = week_data[volume_col].sum()
                    if week_volume > max_weekly_volume:
                        max_weekly_volume = week_volume
                        max_week_start = week_start

                if max_week_start:
                    after_zs_weekly_volume = max_weekly_volume
                    max_week_end = max_week_start + timedelta(days=6)
                    after_zs_weekly_period = f"{max_week_start.strftime('%Y-%m-%d')} 至 {max_week_end.strftime('%Y-%m-%d')}"

        # f. 周成交量对比
        weekly_volume_ratio = after_zs_weekly_volume / zs_weekly_volume if zs_weekly_volume > 0 else 0

        return {
            'zs_avg_volume': zs_avg_volume,
            'after_zs_max_volume': after_zs_max_volume,
            'after_zs_max_date': after_zs_max_date,
            'volume_ratio': volume_ratio,
            'zs_weekly_volume': zs_weekly_volume,
            'zs_weekly_period': zs_weekly_period,
            'after_zs_weekly_volume': after_zs_weekly_volume,
            'after_zs_weekly_period': after_zs_weekly_period,
            'weekly_volume_ratio': weekly_volume_ratio
        }

    except Exception as e:
        logger.error(f"成交量分析出错: {e}")
        return {}


def check_3buy_conditions(stock_code: str, stock_name: str, k_type: KL_TYPE = KL_TYPE.K_DAY) -> Optional[dict]:
    """
    检查单只股票的3买条件

    参数:
        stock_code: 股票代码
        stock_name: 股票名称
        k_type: K线级别

    返回:
        如果符合3买条件，返回包含详细信息的字典，否则返回None
    """
    try:
        # 从QMT获取数据
        period_map = {
            KL_TYPE.K_DAY: '1d',
            KL_TYPE.K_WEEK: '1w',
            KL_TYPE.K_60M: '60m',
            KL_TYPE.K_30M: '30m',
            KL_TYPE.K_15M: '15m',
            KL_TYPE.K_5M: '5m',
            KL_TYPE.K_1M: '1m'
        }



        period = period_map.get(k_type, '1d')

        # 首先下载最新数据（批量分析模式）
        try:
            from Zstock_tech_qmttdx import download_all_data_for_stock
            download_all_data_for_stock(stock_code)
        except Exception:
            pass

        # 特殊处理30分钟K线
        if k_type == KL_TYPE.K_30M:
            df_5m = get_miniqmt_data(stock_code, period='5m', count=3000)

            if df_5m.empty or 'error' in df_5m.columns:
                df = get_miniqmt_data(stock_code, period=period, count=1000)
            else:
                # 将5分钟数据转换为30分钟数据
                df = convert_5min_to_30min(df_5m)
        else:
            df = get_miniqmt_data(stock_code, period=period, count=1000)

        if df.empty or 'error' in df.columns:
            return None

        # 统一列名映射
        column_mapping = {
            '日期': 'time',
            '时间': 'time',
            'date': 'time',
            '开盘': 'open',
            '最高': 'high',
            '最低': 'low',
            '收盘': 'close',
            '成交量': 'volume',
            '成交额': 'amount'
        }

        # 重命名列
        df_renamed = df.rename(columns=column_mapping)

        # 转换数据格式
        csv_lines = []
        seen_timestamps = set()  # 用于检测重复时间戳

        for i, (_, row) in enumerate(df_renamed.iterrows()):
            time_str = str(row['time'])

            # 对于30分钟数据，需要保留完整的时间信息
            if k_type == KL_TYPE.K_30M:
                # 30分钟数据需要包含时间信息
                if '-' in time_str and ' ' in time_str:
                    # 格式: 2024-01-01 09:30:00
                    datetime_part = time_str.split('.')[0]  # 去掉毫秒部分
                    # 缠论库期望的格式是 YYYY/MM/DD HH:MM:SS
                    try:
                        dt = datetime.strptime(datetime_part, '%Y-%m-%d %H:%M:%S')
                        date_part = dt.strftime('%Y/%m/%d %H:%M:%S')
                    except:
                        # 如果解析失败，使用原始格式
                        date_part = datetime_part.replace('-', '/').replace('T', ' ')
                elif '-' in time_str:
                    # 只有日期，这不应该发生在30分钟数据中
                    date_part = time_str.split(' ')[0].replace('-', '/') + ' 09:30:00'
                else:
                    # 格式: 20240101，这也不应该发生在30分钟数据中
                    date_part = f"{time_str[:4]}/{time_str[4:6]}/{time_str[6:8]} 09:30:00"
            else:
                # 日线数据只需要日期
                if '-' in time_str:
                    date_part = time_str.split(' ')[0].replace('-', '/')
                else:
                    date_part = f"{time_str[:4]}/{time_str[4:6]}/{time_str[6:8]}"

            # 检测重复时间戳
            if date_part in seen_timestamps:
                continue
            seen_timestamps.add(date_part)

            # 获取成交量和成交额数据
            volume = row.get('volume', 0)
            amount = row.get('amount', 0)
            turnover_rate = row.get('turnover_rate', 0)

            # 确保数据不为空
            if volume is None or pd.isna(volume):
                volume = 0
            if amount is None or pd.isna(amount):
                amount = 0
            if turnover_rate is None or pd.isna(turnover_rate):
                turnover_rate = 0

            # 现在缠论库支持8列数据：时间+OHLC+成交量+成交额+换手率
            line = f"{date_part},{row['open']},{row['high']},{row['low']},{row['close']},{volume},{amount},{turnover_rate}"
            csv_lines.append(line)

        # 创建临时CSV文件供缠论使用（参考chan_helloworld_en.py的成功做法）
        # 创建临时CSV文件在chan目录下（关键修正）
        chan_dir = os.path.join(os.path.dirname(__file__), 'libs', 'chan')
        if not os.path.exists(chan_dir):
            chan_dir = os.path.dirname(__file__)  # 如果chan目录不存在，使用当前目录

        # 使用chan库期望的文件命名格式
        if CHAN_AVAILABLE:
            period_suffix = {
                KL_TYPE.K_DAY: 'day',
                KL_TYPE.K_WEEK: 'week',
                KL_TYPE.K_30M: '30m'
            }.get(k_type, 'day')
        else:
            period_suffix = period.replace('d', 'day').replace('w', 'week').replace('m', 'm')

        temp_csv_file = os.path.join(chan_dir, f"{stock_code}_{period_suffix}.csv")

        # 写入临时CSV文件
        with open(temp_csv_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(csv_lines))

        # 使用配置排列组合获取最大的中枢上沿
        upper_price, latest_zs, zs_info, best_config, volume_info, temp_csv_file, best_chan_obj = get_latest_zs_upper_price_with_configs(
            stock_code, csv_lines, k_type
        )

        if upper_price is None:
            return None

        # 输出中枢选择的详细信息（与调试模式保持一致）
        print(f"  {zs_info}")

        # 输出最佳配置信息
        if best_config:
            if isinstance(best_config, str):
                print(f"  使用的最佳配置: {best_config}")
            elif isinstance(best_config, dict) and best_config:
                config_str = f"bi_algo={best_config.get('bi_algo', 'N/A')}, seg_algo={best_config.get('seg_algo', 'N/A')}, zs_algo={best_config.get('zs_algo', 'N/A')}"
                print(f"  使用的最佳配置: {config_str}")
            else:
                print(f"  使用的最佳配置: {best_config}")
        else:
            print("  使用的最佳配置: 未获取到配置信息")

        # 使用最佳配置的缠论对象（避免重新创建导致的不一致）
        try:
            if best_chan_obj is None:
                return None

            chan = best_chan_obj

            # 获取K线数据
            kl_data = chan[0]
            if not kl_data or len(kl_data.lst) == 0:
                return None

            # 获取最新K线（CKLine对象包含多个K线单元）
            latest_kline = kl_data.lst[-1]

            # CKLine对象包含多个K线单元，获取最后一个
            if hasattr(latest_kline, 'lst') and latest_kline.lst:
                latest_klu = latest_kline.lst[-1]  # 获取最后一个K线单元
                latest_close = latest_klu.close
                latest_time = latest_klu.time
            else:
                return None

            # 获取最新K线（CKLine对象包含多个K线单元）
            latest_kline = kl_data.lst[-1]

            # CKLine对象包含多个K线单元，获取最后一个
            if hasattr(latest_kline, 'lst') and latest_kline.lst:
                latest_klu = latest_kline.lst[-1]  # 获取最后一个K线单元
                latest_time = latest_klu.time
            else:
                return None

            # 判断中枢是否已确定
            zs_status = "已确定" if latest_zs.is_sure else "未确定"
            print(f"  中枢状态: {zs_status}")

            # 检查是否有出中枢的笔
            if hasattr(latest_zs, 'bi_out') and latest_zs.bi_out:
                print(f"  该中枢有出笔，索引为: {latest_zs.bi_out.idx}")
            else:
                print(f"  该中枢尚未有笔离开")

            # 条件a: 最新周期的收盘价位于中枢上沿之上
            condition_a = latest_close > upper_price
            print(f"  条件a - 收盘价{latest_close:.2f} > 上沿{upper_price:.2f}: {'通过' if condition_a else '不通过'}")

            # 条件b: 最新周期的收盘价不超过中枢上沿的115%
            upper_limit = upper_price * 1.15
            condition_b = latest_close <= upper_limit
            print(f"  条件b - 收盘价{latest_close:.2f} <= 上沿115%({upper_limit:.2f}): {'通过' if condition_b else '不通过'}")

            # 条件c: 在最新价与最近一个收盘价小于中枢上沿的K线之间，最高的K线需小于中枢上沿的1.25倍
            condition_c = True
            max_high_in_range = 0
            upper_125_limit = upper_price * 1.25

            # 找到最近一个收盘价小于中枢上沿的K线位置
            last_below_upper_idx = -1
            for i in range(len(kl_data.lst) - 1, -1, -1):
                kline = kl_data.lst[i]
                if hasattr(kline, 'lst') and kline.lst:
                    klu = kline.lst[-1]  # 获取K线单元
                    if klu.close < upper_price:
                        last_below_upper_idx = i
                        break

            if last_below_upper_idx >= 0:
                # 检查从该位置到最新K线之间的最高价
                for i in range(last_below_upper_idx, len(kl_data.lst)):
                    kline = kl_data.lst[i]
                    if hasattr(kline, 'lst') and kline.lst:
                        klu = kline.lst[-1]  # 获取K线单元
                        max_high_in_range = max(max_high_in_range, klu.high)

                condition_c = max_high_in_range < upper_125_limit
            print(f"  条件c - 区间最高价{max_high_in_range:.2f} < 上沿125%({upper_125_limit:.2f}): {'通过' if condition_c else '不通过'}")

            # 条件d: 中枢结束后的笔数量限制
            condition_d = True
            completed_bi_count = 0

            try:
                # 获取中枢结束位置的索引
                zs_end_idx = latest_zs.end.idx if hasattr(latest_zs, 'end') and hasattr(latest_zs.end, 'idx') else None

                if zs_end_idx is not None and best_chan_obj:
                    kl_data_for_bi = best_chan_obj[0]

                    # 获取笔列表
                    if hasattr(kl_data_for_bi, 'bi_list') and kl_data_for_bi.bi_list:
                        if hasattr(kl_data_for_bi.bi_list, 'lst'):
                            bi_list = kl_data_for_bi.bi_list.lst
                        elif hasattr(kl_data_for_bi.bi_list, '__iter__'):
                            bi_list = list(kl_data_for_bi.bi_list)
                        else:
                            bi_list = kl_data_for_bi.bi_list

                        # 首先检查中枢的出笔信息
                        if hasattr(latest_zs, 'bi_out') and latest_zs.bi_out:
                            out_bi = latest_zs.bi_out
                            out_begin_klu = out_bi.get_begin_klu() if hasattr(out_bi, 'get_begin_klu') else None
                            out_end_klu = out_bi.get_end_klu() if hasattr(out_bi, 'get_end_klu') else None

                            if out_begin_klu and hasattr(out_begin_klu, 'idx'):
                                if out_end_klu and hasattr(out_end_klu, 'idx'):
                                    print(f"  条件d检查: 中枢出笔索引 {out_begin_klu.idx}-{out_end_klu.idx} (完整笔)")
                                else:
                                    print(f"  条件d检查: 中枢出笔索引 {out_begin_klu.idx}-未完成")

                        # 统计中枢结束后的完整笔数量
                        first_bi_after_zs = None
                        for i, bi in enumerate(bi_list):
                            try:
                                # 获取笔的开始K线单元
                                begin_klu = bi.get_begin_klu() if hasattr(bi, 'get_begin_klu') else None
                                end_klu = bi.get_end_klu() if hasattr(bi, 'get_end_klu') else None

                                if begin_klu and hasattr(begin_klu, 'idx'):
                                    bi_start_idx = begin_klu.idx
                                    bi_end_idx = end_klu.idx if end_klu and hasattr(end_klu, 'idx') else None

                                    # 修改判断逻辑：检查笔是否在中枢结束后开始或跨越中枢结束点
                                    if bi_start_idx > zs_end_idx or (bi_end_idx and bi_start_idx <= zs_end_idx < bi_end_idx):
                                        # 检查笔是否已经完全确定（有明确的结束点）
                                        if end_klu and hasattr(end_klu, 'idx'):
                                            # 这是一个完整的笔
                                            if first_bi_after_zs is None:
                                                first_bi_after_zs = bi
                                                # 检查第一个笔是否是向上的出笔
                                                if hasattr(begin_klu, 'close') and hasattr(end_klu, 'close'):
                                                    if end_klu.close <= begin_klu.close:
                                                        # 第一个笔不是向上的，不符合条件
                                                        print(f"  条件d检查: 第一个笔不是向上出笔 (起始价{begin_klu.close:.2f} -> 结束价{end_klu.close:.2f})")
                                                        condition_d = False
                                                        break
                                                    else:
                                                        print(f"  条件d检查: 第一个笔是向上出笔 (起始价{begin_klu.close:.2f} -> 结束价{end_klu.close:.2f})")

                                            completed_bi_count += 1

                                            # 如果超过2个完整笔，则不符合条件
                                            if completed_bi_count > 2:
                                                condition_d = False
                                                break
                            except Exception as e:
                                continue

                        print(f"  条件d检查: 中枢结束后共有{completed_bi_count}个完整笔")

                    else:
                        condition_d = False
                else:
                    condition_d = False

            except Exception as e:
                condition_d = False

            print(f"  条件d - 中枢结束后完整笔数量({completed_bi_count}) <= 2: {'通过' if condition_d else '不通过'}")

            # 输出关键信息（使用logger确保在批量模式下也能看到）
            logger = logging.getLogger()
            logger.info(f"最终确定中枢的上沿值: {upper_price:.2f}")
            logger.info(f"最终确定中枢的起止时间: {volume_info['start_time']} 到 {volume_info['end_time']}")
            logger.info(f"使用的最佳配置: {best_config}")

            # 判断中枢是否完结
            zs_status = "已确定" if latest_zs.is_sure else "未确定"
            if hasattr(latest_zs, 'bi_out') and latest_zs.bi_out:
                bi_out_start_time = None
                bi_out_end_time = None
                # 获取出笔的起始和终止时间
                if hasattr(latest_zs.bi_out, 'begin') and hasattr(latest_zs.bi_out.begin, 'time'):
                    bi_out_start_time = latest_zs.bi_out.begin.time
                if hasattr(latest_zs.bi_out, 'end') and hasattr(latest_zs.bi_out.end, 'time'):
                    bi_out_end_time = latest_zs.bi_out.end.time
                logger.info(f"最终确定中枢是否完结: {zs_status}，出笔的起始终止时间: {bi_out_start_time} 到 {bi_out_end_time}")
            else:
                logger.info(f"最终确定中枢是否完结: {zs_status}，尚未有笔离开")

            # 成交量分析（仅用于信息展示，不影响3买判定）
            logger.info(f"成交量分析:")
            try:
                # 获取中枢时间信息
                zs_start_time = volume_info.get('start_time')
                zs_end_time = volume_info.get('end_time')

                if zs_start_time and zs_end_time:
                    # 进行成交量分析
                    volume_analysis = analyze_volume_in_zs_range(df_renamed, zs_start_time, zs_end_time, logger)

                    if volume_analysis:
                        # a. 中枢内日均成交量
                        logger.info(f"  中枢内日均成交量: {volume_analysis['zs_avg_volume']:.0f}股")

                        # b. 中枢后日成交量峰值
                        if volume_analysis['after_zs_max_volume'] > 0:
                            logger.info(f"  中枢后日成交量峰值: {volume_analysis['after_zs_max_volume']:.0f}股 (日期: {volume_analysis['after_zs_max_date']})")

                            # c. 比较倍数
                            logger.info(f"  中枢后峰值/中枢内均值 = {volume_analysis['volume_ratio']:.2f}倍")
                        else:
                            logger.info(f"  中枢后日成交量峰值: 暂无数据")

                        # d. 中枢内最大周成交量
                        if volume_analysis['zs_weekly_volume'] > 0:
                            logger.info(f"  中枢内最大周成交量: {volume_analysis['zs_weekly_volume']:.0f}股 (周期: {volume_analysis['zs_weekly_period']})")
                        else:
                            logger.info(f"  中枢内最大周成交量: 暂无完整周数据")

                        # e. 中枢后最大周成交量
                        if volume_analysis['after_zs_weekly_volume'] > 0:
                            logger.info(f"  中枢后最大周成交量: {volume_analysis['after_zs_weekly_volume']:.0f}股 (周期: {volume_analysis['after_zs_weekly_period']})")

                            # f. 周成交量对比
                            if volume_analysis['zs_weekly_volume'] > 0:
                                logger.info(f"  中枢后周峰值/中枢内周峰值 = {volume_analysis['weekly_volume_ratio']:.2f}倍")
                        else:
                            logger.info(f"  中枢后最大周成交量: 暂无数据")
                    else:
                        logger.info(f"  成交量分析失败，数据不足")
                else:
                    logger.info(f"  成交量分析跳过，中枢时间信息不完整")
            except Exception as e:
                logger.info(f"  成交量分析出错: {e}")

            # 判断三买过程（使用logger确保在批量模式下也能看到）
            logger.info(f"判断三买过程:")
            logger.info(f"  当前价: {latest_close:.2f}")
            logger.info(f"  中枢上沿: {upper_price:.2f}")
            logger.info(f"  条件a - 收盘价{latest_close:.2f} > 上沿{upper_price:.2f}: {'通过' if condition_a else '不通过'}")
            logger.info(f"  条件b - 收盘价{latest_close:.2f} <= 上沿115%({upper_limit:.2f}): {'通过' if condition_b else '不通过'}")
            logger.info(f"  条件c - 区间最高价{max_high_in_range:.2f} < 上沿125%({upper_125_limit:.2f}): {'通过' if condition_c else '不通过'}")
            logger.info(f"  条件d - 中枢结束后完整笔数量({completed_bi_count}) <= 2: {'通过' if condition_d else '不通过'}")

            # 判断是否符合3买（包含条件d）
            is_3buy = condition_a and condition_b and condition_c and condition_d

            # 输出判断结论并返回结果
            if is_3buy:
                logger.info(f"判断结论: 符合3买条件")
                result = {
                    'stock_code': stock_code,
                    'stock_name': stock_name,
                    'latest_close': latest_close,
                    'zs_upper': upper_price,
                    'zs_lower': latest_zs.low,
                    'zs_mid': latest_zs.mid,
                    'upper_limit': upper_limit,
                    'upper_125_limit': upper_125_limit,
                    'max_high_in_range': max_high_in_range,
                    'latest_time': latest_time,
                    'k_type': k_type,
                    'kline_count': len(csv_lines),  # 添加K线数量
                    'best_config': "默认配置",  # 添加最佳配置
                    'volume_info': volume_info,  # 添加中枢信息
                    'completed_bi_count': completed_bi_count  # 添加中枢结束后完整笔数量
                }
                return result
            else:
                logger.info(f"判断结论: 不符合3买条件")
                return None

        finally:
            # 清理临时CSV文件（如果存在的话）
            try:
                if 'temp_csv_file' in locals() and temp_csv_file and os.path.exists(temp_csv_file):
                    os.remove(temp_csv_file)
            except Exception:
                pass

    except Exception:
        return None


def read_existing_cljx_stocks():
    """
    读取cljx.blk中现有的股票代码
    
    返回:
        List[Tuple[str, str]]: (带前缀代码, 6位代码) 元组列表
    """
    cljx_file = os.path.join(BLOCKNEW_DIR, "cljx.blk")
    
    if not os.path.exists(cljx_file):
        logger.info(f"cljx.blk文件不存在，将创建新文件")
        return []
    
    try:
        # 使用健壮的读取方式
        with open(cljx_file, 'rb') as f:
            content = f.read()
        
        # 解码文本
        text = content.decode('gbk', errors='ignore')
        
        # 分割行并去掉空行
        lines = [line.strip() for line in text.replace('\r\n', '\n').split('\n') if line.strip()]
        
        # 转换为(带前缀代码, 6位代码)格式
        stock_pairs = []
        for line in lines:
            if len(line) == 7 and line.isdigit():
                # 带前缀的7位代码
                prefix_code = line
                six_digit_code = line[1:]  # 去掉前缀
                stock_pairs.append((prefix_code, six_digit_code))
            elif len(line) == 6 and line.isdigit():
                # 6位代码，需要添加前缀
                six_digit_code = line
                prefix_code = add_tdx_market_prefix(six_digit_code)
                stock_pairs.append((prefix_code, six_digit_code))
        
        
        logger.info(f"从cljx.blk读取到{len(stock_pairs)}只现有股票")
        return stock_pairs
        
    except Exception as e:
        logger.error(f"读取cljx.blk文件失败: {e}")
        return []


def clean_existing_stocks():
    """
    清理cljx.blk中不符合3买条件的存量股票
    
    返回:
        Tuple[List[str], List[str]]: (保留的股票代码列表, 清理的股票代码列表)
    """
    logger.info("\n" + "="*60)
    logger.info("开始清理cljx.blk中的存量股票")
    logger.info("="*60)
    
    # 如果缠论模块不可用，则跳过清理
    if not CHAN_AVAILABLE:
        logger.warning("缠论模块不可用，无法执行存量股票清理。跳过此步骤。")
        return [], []
    
    # 读取现有股票
    existing_stocks = read_existing_cljx_stocks()
    
    if not existing_stocks:
        logger.info("cljx.blk中没有现有股票，跳过清理步骤")
        return [], []
    
    logger.info(f"准备验证{len(existing_stocks)}只现有股票的3买条件")
    
    # 存储结果
    valid_stocks = []  # 仍符合条件的股票
    invalid_stocks = []  # 不符合条件需要清理的股票
    
    # 逐个验证现有股票
    for i, (prefix_code, six_digit_code) in enumerate(existing_stocks, 1):
        logger.info(f"\n验证进度: {i}/{len(existing_stocks)} - {six_digit_code}")
        
        # 分析日线级别
        day_qualified = False
        min30_qualified = False
        
        day_result = check_3buy_conditions(six_digit_code, f"存量股票{six_digit_code}", KL_TYPE.K_DAY)
        min30_result = check_3buy_conditions(six_digit_code, f"存量股票{six_digit_code}", KL_TYPE.K_30M)
        
        # 判断是否符合条件（任一级别符合即可）
        day_qualified = day_result is not None
        min30_qualified = min30_result is not None
        
        if day_qualified or min30_qualified:
            valid_stocks.append(prefix_code)
            logger.info(f"  ? 保留 {six_digit_code} - 日线:{'?' if day_qualified else '?'} 30分钟:{'?' if min30_qualified else '?'}")
        else:
            invalid_stocks.append(prefix_code)
            logger.info(f"  ? 清理 {six_digit_code} - 不符合3买条件")
        
        # 添加短暂延迟
        time.sleep(0.1)
    
    # 输出清理统计
    logger.info(f"\n清理统计:")
    logger.info(f"  原有股票: {len(existing_stocks)}只")
    logger.info(f"  保留股票: {len(valid_stocks)}只")
    logger.info(f"  清理股票: {len(invalid_stocks)}只")
    
    if invalid_stocks:
        # 将清理的股票代码转换为6位格式显示
        invalid_six_digit = [code[1:] if len(code) == 7 else code for code in invalid_stocks]
        logger.info(f"  清理的股票代码: {', '.join(invalid_six_digit)}")
    
    # 更新cljx.blk文件
    if valid_stocks:
        cljx_file = os.path.join(BLOCKNEW_DIR, "cljx.blk")
        success = write_block_file(valid_stocks, cljx_file, append=False)
        if success:
            logger.info(f"? 已更新cljx.blk，保留{len(valid_stocks)}只符合条件的股票")
        else:
            logger.error(f"? 更新cljx.blk失败")
    else:
        # 如果没有符合条件的股票，清空文件
        cljx_file = os.path.join(BLOCKNEW_DIR, "cljx.blk")
        try:
            with open(cljx_file, 'w', encoding='gbk'):
                pass  # 创建空文件
            logger.info("? cljx.blk已清空（没有符合条件的股票）")
        except Exception as e:
            logger.error(f"? 清空cljx.blk失败: {e}")
    
    return valid_stocks, invalid_stocks


def analyze_stocks_from_xls(xls_file: str, k_types = None):
    """
    从XLS文件分析股票的3买信号
    根据用户选择分析指定的时间级别

    参数:
        xls_file: XLS文件路径
        k_types: 要分析的K线级别列表
    """
    if k_types is None:
        k_types = [KL_TYPE.K_DAY, KL_TYPE.K_30M]

    # 显示分析级别
    level_names = {
        KL_TYPE.K_DAY: "日线",
        KL_TYPE.K_30M: "30分钟"
    }
    selected_levels = [level_names.get(k, str(k)) for k in k_types]

    logger.info("=" * 80)
    logger.info("缠论3买点检测程序")
    logger.info(f"分析文件: {xls_file}")
    logger.info(f"分析级别: {', '.join(selected_levels)}")
    if len(k_types) > 1:
        logger.info("只要任一级别满足条件即视为符合要求")
    logger.info("=" * 80)

    # 检查缠论模块是否可用
    if not CHAN_AVAILABLE:
        logger.error("缠论模块不可用，无法执行分析。请确保libs/chan目录完整且可用。")
        return

    # 检查文件是否存在
    if not os.path.exists(xls_file):
        logger.error(f"文件不存在: {xls_file}")
        return

    # 连接QMT
    logger.info("\n1. 连接QMT...")
    if not connect_miniqmt():
        logger.error("QMT连接失败，请检查QMT客户端是否运行")
        return
    logger.info("QMT连接成功")

    # 【修改】取消首次扫描已有股票的逻辑，直接处理输入的股票列表
    # logger.info("\n2. 清理cljx.blk中的存量股票...")
    # _, _ = clean_existing_stocks()  # 忽略返回值
    logger.info("\n2. 跳过存量股票清理步骤，直接处理输入股票列表...")

    # 读取股票列表
    logger.info(f"\n3. 从Excel文件读取股票列表...")
    stock_list = read_tdx_xls(xls_file)

    if not stock_list:
        logger.error("文件中未找到有效股票")
        return

    logger.info(f"共找到{len(stock_list)}只股票")

    logger.info(f"\n4. 开始缠论3买信号分析...")
    logger.info("-" * 80)

    # 存储符合条件的股票
    qualified_stocks = []

    # 逐个分析股票
    for i, (stock_code, stock_name) in enumerate(stock_list, 1):
        logger.info(f"\n{'='*60}")
        logger.info(f"进度: {i}/{len(stock_list)} - 分析股票: {stock_code} {stock_name}")
        logger.info(f"{'='*60}")

        # 存储该股票在各级别的分析结果
        stock_results = {
            'stock_code': stock_code,
            'stock_name': stock_name,
            'day_result': None,
            '30m_result': None,
            'qualified': False
        }

        # 根据用户选择的级别进行分析
        if KL_TYPE.K_DAY in k_types:
            logger.info(f"\n【日线级别分析】")
            logger.info("-" * 40)
            day_result = check_3buy_conditions(stock_code, stock_name, KL_TYPE.K_DAY)
            stock_results['day_result'] = day_result
            if day_result:
                logger.info(f"  ? 日线级别符合3买条件!")
                stock_results['qualified'] = True
            else:
                logger.info(f"  ? 日线级别不符合3买条件")

        if KL_TYPE.K_30M in k_types:
            logger.info(f"\n【30分钟级别分析】")
            logger.info("-" * 40)
            min30_result = check_3buy_conditions(stock_code, stock_name, KL_TYPE.K_30M)
            stock_results['30m_result'] = min30_result
            if min30_result:
                logger.info(f"  ? 30分钟级别符合3买条件!")
                stock_results['qualified'] = True
            else:
                logger.info(f"  ? 30分钟级别不符合3买条件")

        # 综合判断
        logger.info(f"\n【综合判断】")
        logger.info("-" * 40)
        if stock_results['qualified']:
            logger.info(f"  ? 该股票符合3买要求（至少一个级别满足条件）")
            qualified_stocks.append(stock_results)
        else:
            logger.info(f"  ? 该股票不符合3买要求（两个级别都不满足条件）")

        # 添加短暂延迟避免请求过快
        time.sleep(0.1)

    # 输出最终汇总结果
    logger.info(f"\n{'='*80}")
    logger.info(f"最终汇总结果")
    logger.info(f"{'='*80}")

    if qualified_stocks:
        logger.info(f"共找到{len(qualified_stocks)}只符合3买条件的股票:")
        logger.info("")

        for stock_result in qualified_stocks:
            logger.info(f"股票: {stock_result['stock_code']} {stock_result['stock_name']}")

            # 根据分析的级别显示日线结果
            if KL_TYPE.K_DAY in k_types:
                day_result = stock_result['day_result']
                if day_result:
                    logger.info(f"  【日线级别】? 符合条件")
                    logger.info(f"    最新收盘价: {day_result['latest_close']:.2f}")
                    logger.info(f"    中枢上沿: {day_result['zs_upper']:.2f}, 中枢下沿: {day_result['zs_lower']:.2f}")
                    logger.info(f"    上沿115%限制: {day_result['upper_limit']:.2f}")
                    if 'upper_125_limit' in day_result and 'max_high_in_range' in day_result:
                        logger.info(f"    上沿125%限制: {day_result['upper_125_limit']:.2f}, 区间最高价: {day_result['max_high_in_range']:.2f}")

                    # 添加K线数量信息
                    if 'kline_count' in day_result:
                        logger.info(f"    使用K线数量: {day_result['kline_count']}根")
                    # 添加最佳配置信息
                    if 'best_config' in day_result:
                        logger.info(f"    最佳配置: {day_result['best_config']}")
                    # 添加中枢详细信息
                    if 'volume_info' in day_result and day_result['volume_info']:
                        vol_info = day_result['volume_info']
                        logger.info(f"    中枢时间: {vol_info['start_time']} 到 {vol_info['end_time']}")
                        # 安全检查成交量信息是否存在
                        if 'max_volume' in vol_info and vol_info['max_volume'] > 0:
                            logger.info(f"    成交量峰值: {vol_info['max_volume']:.0f} (时间: {vol_info['max_volume_time']}, 价格: {vol_info['max_volume_price']:.2f})")
                else:
                    logger.info(f"  【日线级别】? 不符合条件")

            # 根据分析的级别显示30分钟结果
            if KL_TYPE.K_30M in k_types:
                min30_result = stock_result['30m_result']
                if min30_result:
                    logger.info(f"  【30分钟级别】? 符合条件")
                    logger.info(f"    最新收盘价: {min30_result['latest_close']:.2f}")
                    logger.info(f"    中枢上沿: {min30_result['zs_upper']:.2f}, 中枢下沿: {min30_result['zs_lower']:.2f}")
                    logger.info(f"    上沿115%限制: {min30_result['upper_limit']:.2f}")
                    if 'upper_125_limit' in min30_result and 'max_high_in_range' in min30_result:
                        logger.info(f"    上沿125%限制: {min30_result['upper_125_limit']:.2f}, 区间最高价: {min30_result['max_high_in_range']:.2f}")

                    # 添加K线数量信息
                    if 'kline_count' in min30_result:
                        logger.info(f"    使用K线数量: {min30_result['kline_count']}根")
                    # 添加最佳配置信息
                    if 'best_config' in min30_result:
                        logger.info(f"    最佳配置: {min30_result['best_config']}")
                    # 添加中枢详细信息
                    if 'volume_info' in min30_result and min30_result['volume_info']:
                        vol_info = min30_result['volume_info']
                        logger.info(f"    中枢时间: {vol_info['start_time']} 到 {vol_info['end_time']}")
                        # 安全检查成交量信息是否存在
                        if 'max_volume' in vol_info and vol_info['max_volume'] > 0:
                            logger.info(f"    成交量峰值: {vol_info['max_volume']:.0f} (时间: {vol_info['max_volume_time']}, 价格: {vol_info['max_volume_price']:.2f})")
                else:
                    logger.info(f"  【30分钟级别】? 不符合条件")

            logger.info("")

        # 将符合条件的股票代码写入cljx.blk文件
        logger.info(f"\n{'='*80}")
        logger.info(f"写入cljx.blk文件")
        logger.info(f"{'='*80}")

        # 提取股票代码
        qualified_codes = [stock_result['stock_code'] for stock_result in qualified_stocks]

        # 【关键修改】添加通达信市场前缀
        logger.info(f"添加通达信市场前缀...")
        prefixed_codes = []
        for code in qualified_codes:
            prefixed_code = add_tdx_market_prefix(code)
            prefixed_codes.append(prefixed_code)
            if prefixed_code != code:
                logger.info(f"  {code} -> {prefixed_code}")
        
        logger.info(f"添加前缀后股票代码: {len(prefixed_codes)}只")

        # 过滤股票代码（去掉创业板等）
        logger.info(f"过滤前股票代码: {len(prefixed_codes)}只")
        filtered_codes = filter_stock_codes(prefixed_codes)
        logger.info(f"过滤后股票代码: {len(filtered_codes)}只")

        if filtered_codes:
            # 【修改】写入cljx.blk文件（覆盖模式，先清空再添加）
            cljx_file = os.path.join(BLOCKNEW_DIR, "cljx.blk")
            logger.info(f"目标文件: {cljx_file}")

            success = write_block_file(filtered_codes, cljx_file, append=False)

            if success:
                logger.info(f"? 成功将{len(filtered_codes)}只股票覆盖写入cljx.blk文件")
                logger.info(f"写入的股票代码: {', '.join(filtered_codes)}")
                logger.info(f"注意：代码已添加通达信市场前缀，通达信中应该可以正常显示")
                logger.info(f"注意：已清空原有内容，仅保留本次发现的3买股票")
            else:
                logger.error(f"? 写入cljx.blk文件失败")
        else:
            logger.info("过滤后没有有效的股票代码，跳过写入")
    else:
        logger.info("未找到符合3买条件的股票")

    logger.info("-" * 80)

















def debug_single_stock(stock_code: str, k_types: List[KL_TYPE] = None):
    """
    调试模式：详细分析单个股票的3买条件

    参数:
        stock_code: 6位股票代码
        k_types: 要分析的K线级别列表
    """
    # 设置日志系统
    logger = setup_logging()

    if k_types is None:
        k_types = [KL_TYPE.K_DAY, KL_TYPE.K_30M]

    print("=" * 50)
    print("缠论3买点检测程序 - 调试模式")
    print(f"分析股票: {stock_code}")

    logger.info("=" * 80)
    logger.info("缠论3买点检测程序 - 调试模式")
    logger.info(f"分析股票: {stock_code}")

    # 显示分析级别
    level_names = {
        KL_TYPE.K_DAY: "日线",
        KL_TYPE.K_30M: "30分钟"
    }
    selected_levels = [level_names.get(k, str(k)) for k in k_types]
    print(f"分析级别: {', '.join(selected_levels)}")
    print("=" * 50)

    logger.info(f"分析级别: {', '.join(selected_levels)}")
    logger.info("=" * 80)

    # 检查缠论模块是否可用
    if not CHAN_AVAILABLE:
        logger.error("缠论模块不可用，无法执行分析。请确保libs/chan目录完整且可用。")
        return

    # 连接QMT
    logger.info("\n1. 连接QMT...")
    if not connect_miniqmt():
        logger.error("QMT连接失败，请检查QMT客户端是否运行")
        return
    logger.info("QMT连接成功")

    # 清理股票代码
    cleaned_code = clean_stock_code(stock_code)
    if len(cleaned_code) != 6 or not cleaned_code.isdigit():
        logger.error(f"无效的股票代码: {stock_code} -> {cleaned_code}")
        return

    logger.info(f"\n2. 开始详细分析股票: {cleaned_code}")
    logger.info("=" * 60)

    results = {}
    
    # 根据选择的级别进行分析
    if KL_TYPE.K_DAY in k_types:
        print("\n正在分析日线级别...")
        logger.info(f"\n【日线级别详细分析】")
        logger.info("=" * 40)
        day_result = check_3buy_conditions_debug(cleaned_code, f"股票{cleaned_code}", KL_TYPE.K_DAY)
        results['day'] = day_result
        print("日线级别分析完成")

    if KL_TYPE.K_30M in k_types:
        print("\n正在分析30分钟级别...")
        logger.info(f"\n【30分钟级别详细分析】")
        logger.info("=" * 40)
        min30_result = check_3buy_conditions_debug(cleaned_code, f"股票{cleaned_code}", KL_TYPE.K_30M)
        results['30m'] = min30_result
        print("30分钟级别分析完成")

    # 综合判断
    logger.info(f"\n【综合判断结果】")
    logger.info("=" * 40)

    qualified_levels = []
    for level_key, result in results.items():
        level_name = "日线级别" if level_key == 'day' else "30分钟级别"
        is_qualified = result is not None
        status = "? 符合3买条件" if is_qualified else "? 不符合3买条件"
        logger.info(f"{level_name}: {status}")
        if is_qualified:
            qualified_levels.append(level_name)

    overall_qualified = len(qualified_levels) > 0
    
    logger.info(f"")
    if overall_qualified:
        logger.info(f"最终结论: ? 该股票符合3买要求")
        logger.info(f"符合条件的级别: {', '.join(qualified_levels)}")
        logger.info(f"\n推荐操作: 可考虑买入")
    else:
        logger.info(f"最终结论: ? 该股票不符合3买要求")
        logger.info(f"所有分析级别都不满足条件")
        logger.info(f"\n推荐操作: 暂不建议买入")

    logger.info(f"\n调试分析完成!")
    logger.info(f"详细日志已保存到: {LOG_FILE}")


def calculate_profit_loss_ratio(latest_zs, current_price: float, best_chan_obj, logger, max_high_in_range: float = 0.0) -> Tuple[float, Optional[dict]]:
    """
    计算盈亏比 - 双重算法取最大值

    入笔定义：形成中枢前的一笔（即a段），从该笔结束后的下一笔就正式进入了中枢

    算法1（现有算法）：
    1. 情况a: c > 0 时，盈亏比 = (a - (x-zg)) / (x - zg)
    2. 情况b: c ≤ 0 且 b ≥ 0 时，盈亏比 = |c| / b
    3. 情况c: b < 0 时，盈亏比 = 0

    算法2（新增算法）：
    1. c > 0 时，盈亏比 = (a2 - (x-zg)) / (x-zg)，其中 a2 = zg - zd
    2. c ≤ 0 时，使用算法1的相应公式

    最终结果：取两种算法中的最大值

    参数:
        latest_zs: 最新中枢对象
        current_price: 当前价格
        best_chan_obj: 最佳缠论对象
        logger: 日志对象
        max_high_in_range: 中枢结束后至今的区间最高价

    返回:
        (盈亏比, 详细信息字典) 或 (错误信息, None)
    """
    try:
        # 获取中枢的上沿和下沿
        zs_upper = latest_zs.high
        zs_lower = latest_zs.low

        # 查找入笔的起点价格（形成中枢前的一笔，即a段）
        entry_price = None
        entry_start_date = None
        entry_end_date = None

        # 方法1: 从中枢对象获取入笔信息
        if hasattr(latest_zs, 'bi_in') and latest_zs.bi_in:
            # 获取入笔的起始价格（最低价）和日期
            if hasattr(latest_zs.bi_in, 'begin'):
                if hasattr(latest_zs.bi_in.begin, 'low'):
                    entry_price = latest_zs.bi_in.begin.low
                elif hasattr(latest_zs.bi_in.begin, 'val'):
                    entry_price = latest_zs.bi_in.begin.val

                if hasattr(latest_zs.bi_in.begin, 'time'):
                    entry_start_date = latest_zs.bi_in.begin.time

            if hasattr(latest_zs.bi_in, 'end') and hasattr(latest_zs.bi_in.end, 'time'):
                entry_end_date = latest_zs.bi_in.end.time

            if entry_price:
                logger.info(f"  从中枢入笔获取起点最低价: {entry_price:.2f}")

        # 方法2: 如果没有入笔信息，从缠论对象的笔列表中查找
        if entry_price is None and best_chan_obj:
            try:
                kl_data = best_chan_obj[0]
                if hasattr(kl_data, 'bi_list') and kl_data.bi_list:
                    # 修复：正确访问笔列表
                    if hasattr(kl_data.bi_list, 'lst'):
                        bi_list = kl_data.bi_list.lst
                    elif hasattr(kl_data.bi_list, '__iter__'):
                        bi_list = list(kl_data.bi_list)
                    else:
                        # 如果bi_list本身就是列表
                        bi_list = kl_data.bi_list

                    # 查找中枢开始前的最后一笔（即入笔，a段）
                    zs_start_idx = latest_zs.begin.idx if hasattr(latest_zs, 'begin') and latest_zs.begin else 0

                    # 简化的笔分析：查找入笔用于盈亏比计算
                    for bi in reversed(bi_list):
                        try:
                            # 获取笔的开始和结束K线单元
                            begin_klu = bi.get_begin_klu() if hasattr(bi, 'get_begin_klu') else None
                            end_klu = bi.get_end_klu() if hasattr(bi, 'get_end_klu') else None

                            # 获取笔的索引
                            bi_end_idx = end_klu.idx if end_klu and hasattr(end_klu, 'idx') else None

                            # 检查是否找到了有效的结束索引，且在中枢开始前
                            if bi_end_idx is not None and bi_end_idx <= zs_start_idx:
                                # 找到中枢开始前的笔（入笔）
                                if begin_klu:
                                    if hasattr(begin_klu, 'low'):
                                        entry_price = begin_klu.low
                                    elif hasattr(begin_klu, 'val'):
                                        entry_price = begin_klu.val

                                    entry_start_date = begin_klu.time if hasattr(begin_klu, 'time') else None
                                    entry_end_date = end_klu.time if hasattr(end_klu, 'time') else None

                                    logger.info(f"  找到入笔起点价格: {entry_price:.2f}")
                                    break

                        except Exception:
                            continue
            except Exception as e:
                logger.info(f"  从笔列表获取入笔起点失败: {e}")

        # 方法3: 如果还是没有，使用中枢开始位置的最低价
        if entry_price is None and best_chan_obj:
            try:
                kl_data = best_chan_obj[0]
                zs_start_idx = latest_zs.begin.idx if hasattr(latest_zs, 'begin') and latest_zs.begin else 0

                if zs_start_idx > 0 and zs_start_idx < len(kl_data.lst):
                    start_kline = kl_data.lst[zs_start_idx]
                    if hasattr(start_kline, 'lst') and start_kline.lst:
                        start_klu = start_kline.lst[0]  # 取第一个K线单元
                        entry_price = start_klu.low  # 使用最低价作为入笔起点
                        if hasattr(start_klu, 'time'):
                            entry_start_date = start_klu.time
                        logger.info(f"  从中枢开始位置获取入笔起点最低价: {entry_price:.2f}")
            except Exception as e:
                logger.info(f"  从中枢开始位置获取入笔起点失败: {e}")

        if entry_price is None:
            return "无法找到入笔起点价格", None

        # 输出入笔信息
        logger.info(f"  入笔起点价格: {entry_price:.2f}")
        if entry_start_date:
            logger.info(f"  入笔起始日期: {entry_start_date}")
        if entry_end_date:
            logger.info(f"  入笔结束日期: {entry_end_date}")
        logger.info(f"  中枢下沿价格: {zs_lower:.2f}")
        logger.info(f"  中枢上沿价格: {zs_upper:.2f}")
        logger.info(f"  当前价格: {current_price:.2f}")

        # 计算盈亏比公式中的各个要素
        # a = 入笔起点日的最低价与中枢下沿价格的绝对值（算法1使用）
        a = abs(entry_price - zs_lower)
        # a2 = 中枢高度（算法2使用）
        a2 = zs_upper - zs_lower
        b = current_price - zs_lower     # 现价与中枢下沿的差值
        c = current_price - zs_upper     # 现价与中枢上沿的差值
        x = max_high_in_range           # 中枢结束后至今的区间最高价
        zg = zs_upper                   # 中枢上沿价格
        zd = zs_lower                   # 中枢下沿价格

        # 输出所有变量的计算过程
        logger.info(f"  a = |入笔起点 - 中枢下沿| = |{entry_price:.2f} - {zd:.2f}| = {a:.2f} (算法1)")
        logger.info(f"  a2 = 中枢高度 = zg - zd = {zg:.2f} - {zd:.2f} = {a2:.2f} (算法2)")
        logger.info(f"  b = 现价 - 中枢下沿 = {current_price:.2f} - {zd:.2f} = {b:.2f}")
        logger.info(f"  c = 现价 - 中枢上沿 = {current_price:.2f} - {zg:.2f} = {c:.2f}")
        logger.info(f"  x = 中枢结束后至今区间最高价 = {x:.2f} (仅供参考，不用于盈亏比计算)")
        logger.info(f"  zg = 中枢上沿价格 = {zg:.2f}")
        logger.info(f"  zd = 中枢下沿价格 = {zd:.2f}")
        logger.info(f"  current_price = 当前价格 = {current_price:.2f} (用于盈亏比计算)")

        # 算法1计算
        logger.info(f"\n  === 算法1计算 ===")
        profit_loss_ratio_1 = 0.0
        algo1_case = ""

        if c > 0:
            # 情况a: 现价在中枢上沿之上时，盈亏比 = (a - (current_price-zg)) / (current_price - zg)
            denominator = current_price - zg
            if denominator == 0:
                return "算法1分母(current_price - zg)为0，无法计算盈亏比", None
            profit_loss_ratio_1 = (a - denominator) / denominator
            algo1_case = "情况a: c > 0 (现价在中枢上沿之上)"
            logger.info(f"  {algo1_case}")
            logger.info(f"  算法1盈亏比 = (a - (current_price-zg)) / (current_price - zg) = ({a:.2f} - ({current_price:.2f}-{zg:.2f})) / ({current_price:.2f} - {zg:.2f}) = {profit_loss_ratio_1:.4f}")
        elif c <= 0 and b >= 0:
            # 情况b: 现价在中枢内时，盈亏比 = |c| / b
            if b == 0:
                return "算法1分母b为0，无法计算盈亏比", None
            profit_loss_ratio_1 = abs(c) / b
            algo1_case = "情况b: c <= 0 且 b >= 0 (现价在中枢内)"
            logger.info(f"  {algo1_case}")
            logger.info(f"  算法1盈亏比 = |c| / b = |{c:.2f}| / {b:.2f} = {profit_loss_ratio_1:.4f}")
        else:
            # 情况c: b < 0，现价在中枢下沿之下，盈亏比直接等于0
            profit_loss_ratio_1 = 0.0
            algo1_case = "情况c: b < 0 (现价在中枢下沿之下)"
            logger.info(f"  {algo1_case}")
            logger.info(f"  算法1盈亏比 = 0 (固定值)")

        # 算法2计算
        logger.info(f"\n  === 算法2计算 ===")
        profit_loss_ratio_2 = 0.0
        algo2_case = ""

        if c > 0:
            # 现价在中枢上沿之上时，盈亏比 = (a2 - (current_price-zg)) / (current_price-zg)
            denominator = current_price - zg
            if denominator == 0:
                return "算法2分母(current_price - zg)为0，无法计算盈亏比", None
            profit_loss_ratio_2 = (a2 - denominator) / denominator
            algo2_case = "c > 0 (现价在中枢上沿之上)"
            logger.info(f"  {algo2_case}")
            logger.info(f"  算法2盈亏比 = (a2 - (current_price-zg)) / (current_price - zg) = ({a2:.2f} - ({current_price:.2f}-{zg:.2f})) / ({current_price:.2f} - {zg:.2f}) = {profit_loss_ratio_2:.4f}")
        else:
            # 现价在中枢上沿之下时，使用算法1的相应公式
            profit_loss_ratio_2 = profit_loss_ratio_1
            algo2_case = f"c <= 0 (现价在中枢上沿之下)，使用算法1结果"
            logger.info(f"  {algo2_case}")
            logger.info(f"  算法2盈亏比 = {profit_loss_ratio_2:.4f} (与算法1相同)")

        # 比较两种算法，取最大值
        logger.info(f"\n  === 算法比较 ===")
        logger.info(f"  算法1结果: {profit_loss_ratio_1:.4f}")
        logger.info(f"  算法2结果: {profit_loss_ratio_2:.4f}")

        if profit_loss_ratio_1 >= profit_loss_ratio_2:
            profit_loss_ratio = profit_loss_ratio_1
            selected_algorithm = "算法1"
            selected_case = algo1_case
        else:
            profit_loss_ratio = profit_loss_ratio_2
            selected_algorithm = "算法2"
            selected_case = algo2_case

        logger.info(f"  选择: {selected_algorithm} (较大值)")
        logger.info(f"  最终盈亏比: {profit_loss_ratio:.4f}")

        # 检查计算结果的合理性
        if profit_loss_ratio < 0:
            logger.warning(f"  警告: 最终盈亏比为负值 {profit_loss_ratio:.4f}，这可能不合理")

        # 构建详细信息
        ratio_details = {
            'entry_price': entry_price,
            'entry_start_date': entry_start_date,
            'entry_end_date': entry_end_date,
            'zs_lower': zs_lower,
            'zs_upper': zs_upper,
            'current_price': current_price,
            'a': a,
            'a2': a2,
            'b': b,
            'c': c,
            'x': x,
            'zg': zg,
            'zd': zd,
            'algorithm1_ratio': profit_loss_ratio_1,
            'algorithm1_case': algo1_case,
            'algorithm2_ratio': profit_loss_ratio_2,
            'algorithm2_case': algo2_case,
            'selected_algorithm': selected_algorithm,
            'selected_case': selected_case,
            'final_ratio': profit_loss_ratio,
            'calculation_case': f"{selected_algorithm}: {selected_case}"
        }

        return profit_loss_ratio, ratio_details

    except Exception as e:
        logger.error(f"计算盈亏比时发生错误: {e}")
        return f"计算错误: {e}", None


def get_current_price(stock_code: str) -> Optional[float]:
    """
    获取股票的当前价格（实时价格）
    重构版本：直接从QMT下载最新日线数据，获取最新收盘价

    参数:
        stock_code: 6位股票代码

    返回:
        当前价格，如果获取失败返回None
    """
    # 获取logger
    logger = logging.getLogger()

    try:
        # 直接从QMT下载最新日线数据并获取最新收盘价
        try:
            from Zstock_tech_qmttdx import download_miniqmt_data
            from datetime import datetime, timedelta

            # 格式化股票代码
            if stock_code.startswith('6'):
                formatted_code = f"{stock_code}.SH"
            elif stock_code.startswith(('0', '3')):
                formatted_code = f"{stock_code}.SZ"
            else:
                formatted_code = f"{stock_code}.SH"  # 默认上海

            # 下载最新的日线数据
            today = datetime.now().strftime('%Y%m%d')
            yesterday = (datetime.now() - timedelta(days=3)).strftime('%Y%m%d')  # 多下载几天确保有数据

            logger.info(f"  下载最新日线数据...")
            download_miniqmt_data(formatted_code, '1d', yesterday, today)
            logger.info(f"  ? 日线数据下载完成")

            # 获取最新的日线数据
            df_daily = get_miniqmt_data(stock_code, period='1d', count=5)
            if not df_daily.empty and 'error' not in df_daily.columns:
                # 使用中文列名访问数据
                latest_close = df_daily.iloc[-1]['收盘']
                latest_time = df_daily.iloc[-1]['日期']
                logger.info(f"  ? 从日线K线获取最新收盘价: {latest_close:.2f} (日期: {latest_time})")
                return float(latest_close)
            else:
                logger.info(f"  日线数据获取失败或为空")

        except Exception as e:
            logger.info(f"  QMT日线数据获取失败: {e}")

        # 如果QMT日线数据获取失败，返回None
        logger.warning(f"  ? 无法获取当前价格，将使用历史K线最后价格")
        return None

    except Exception as e:
        logger.error(f"  ? 获取当前价格出错: {e}")
        return None


def check_3buy_conditions_debug(stock_code: str, stock_name: str, k_type: KL_TYPE = KL_TYPE.K_DAY) -> Optional[dict]:
    """
    检查单只股票的3买条件（调试版本，输出更详细的信息）

    参数:
        stock_code: 股票代码
        stock_name: 股票名称
        k_type: K线级别

    返回:
        如果符合3买条件，返回包含详细信息的字典，否则返回None
    """
    # 获取logger
    logger = logging.getLogger()

    try:
        # 从QMT获取数据
        period_map = {
            KL_TYPE.K_DAY: '1d',
            KL_TYPE.K_WEEK: '1w',
            KL_TYPE.K_60M: '60m',
            KL_TYPE.K_30M: '30m',
            KL_TYPE.K_15M: '15m',
            KL_TYPE.K_5M: '5m',
            KL_TYPE.K_1M: '1m'
        }

        period = period_map.get(k_type, '1d')

        # 首先下载最新数据
        try:
            from Zstock_tech_qmttdx import download_all_data_for_stock
            download_all_data_for_stock(stock_code)
        except Exception:
            pass

        # 直接使用xtdata.get_market_data获取数据，参考Zstock_tech_qmttdx_bak.py
        formatted_code = stock_code
        if len(stock_code) == 6:
            if stock_code.startswith(('60', '68', '90')):
                formatted_code = stock_code + '.SH'
            elif stock_code.startswith(('00', '30', '12', '20')):
                formatted_code = stock_code + '.SZ'

        # 使用get_miniqmt_data获取数据，增加数据量确保包含完整中枢
        df = get_miniqmt_data(stock_code, period=period, count=1000)

        if df.empty or 'error' in df.columns:
            return None

        # 获取当前实时价格
        current_price = get_current_price(stock_code)

        # 创建原始数据副本用于图表生成
        df_raw = df.copy()

        # 统一列名映射
        column_mapping = {
            '日期': 'time',
            '时间': 'time',
            'date': 'time',
            '开盘': 'open',
            '最高': 'high',
            '最低': 'low',
            '收盘': 'close',
            '成交量': 'volume',
            '成交额': 'amount'
        }

        # 重命名列
        df_renamed = df.rename(columns=column_mapping)

        # 转换数据格式
        csv_lines = []
        seen_timestamps = set()  # 用于检测重复时间戳

        for i, (_, row) in enumerate(df_renamed.iterrows()):
            time_str = str(row['time'])

            # 对于30分钟数据，需要保留完整的时间信息
            if k_type == KL_TYPE.K_30M:
                # 30分钟数据需要包含时间信息
                if '-' in time_str and ' ' in time_str:
                    # 格式: 2024-01-01 09:30:00
                    datetime_part = time_str.split('.')[0]  # 去掉毫秒部分
                    # 缠论库期望的格式是 YYYY/MM/DD HH:MM:SS
                    try:
                        dt = datetime.strptime(datetime_part, '%Y-%m-%d %H:%M:%S')
                        date_part = dt.strftime('%Y/%m/%d %H:%M:%S')
                    except:
                        # 如果解析失败，使用原始格式
                        date_part = datetime_part.replace('-', '/').replace('T', ' ')
                elif '-' in time_str:
                    # 只有日期，这不应该发生在30分钟数据中
                    date_part = time_str.split(' ')[0].replace('-', '/') + ' 09:30:00'
                else:
                    # 格式: 20240101，这也不应该发生在30分钟数据中
                    date_part = f"{time_str[:4]}/{time_str[4:6]}/{time_str[6:8]} 09:30:00"
            else:
                # 日线数据只需要日期
                if '-' in time_str:
                    date_part = time_str.split(' ')[0].replace('-', '/')
                else:
                    date_part = f"{time_str[:4]}/{time_str[4:6]}/{time_str[6:8]}"

            # 检测重复时间戳
            if date_part in seen_timestamps:
                continue
            seen_timestamps.add(date_part)

            # 获取成交量和成交额数据
            volume = row.get('volume', 0)
            amount = row.get('amount', 0)
            turnover_rate = row.get('turnover_rate', 0)

            # 确保数据不为空
            if volume is None or pd.isna(volume):
                volume = 0
            if amount is None or pd.isna(amount):
                amount = 0
            if turnover_rate is None or pd.isna(turnover_rate):
                turnover_rate = 0

            # 现在缠论库支持8列数据：时间+OHLC+成交量+成交额+换手率
            line = f"{date_part},{row['open']},{row['high']},{row['low']},{row['close']},{volume},{amount},{turnover_rate}"
            csv_lines.append(line)

        # 调试：检查时间戳的唯一性
        if k_type == KL_TYPE.K_30M and len(csv_lines) > 0:
            timestamps = [line.split(',')[0] for line in csv_lines]
            unique_timestamps = set(timestamps)
            if len(timestamps) != len(unique_timestamps):
                logger.error(f"  ? 发现重复时间戳！总数={len(timestamps)}, 唯一数={len(unique_timestamps)}")
                # 显示前几个和后几个时间戳
                logger.info(f"  前5个时间戳: {timestamps[:5]}")
                logger.info(f"  后5个时间戳: {timestamps[-5:]}")
                # 找出重复的时间戳
                from collections import Counter
                timestamp_counts = Counter(timestamps)
                duplicates = [ts for ts, count in timestamp_counts.items() if count > 1]
                logger.info(f"  重复的时间戳: {duplicates[:5]}")  # 只显示前5个
            else:
                logger.info(f"  ? 时间戳检查通过，无重复")

        # 创建临时CSV文件供缠论使用
        chan_dir = os.path.join(os.path.dirname(__file__), 'libs', 'chan')
        if not os.path.exists(chan_dir):
            chan_dir = os.path.dirname(__file__)  # 如果chan目录不存在，使用当前目录

        # 使用chan库期望的文件命名格式
        if CHAN_AVAILABLE:
            period_suffix = {
                KL_TYPE.K_DAY: 'day',
                KL_TYPE.K_WEEK: 'week',
                KL_TYPE.K_30M: '30m'
            }.get(k_type, 'day')
        else:
            period_suffix = period.replace('d', 'day').replace('w', 'week').replace('m', 'm')

        temp_csv_file = os.path.join(chan_dir, f"{stock_code}_{period_suffix}.csv")

        # 写入临时CSV文件
        with open(temp_csv_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(csv_lines))

        # 使用配置排列组合获取最大的中枢上沿（调试模式，生成所有配置图表）
        upper_price, latest_zs, _, best_config, volume_info, temp_csv_file_from_config, best_chan_obj = get_latest_zs_upper_price_with_configs(
            stock_code, csv_lines, k_type, debug_mode=True, stock_name=stock_name, df_raw=df_raw
        )

        if upper_price is None:
            logger.error("上沿价格获取失败，程序返回")
            return None

        # 使用最佳配置的缠论对象（避免重新创建导致的不一致）
        try:
            if best_chan_obj is None:
                logger.error("最佳缠论对象为空，程序返回")
                return None

            chan = best_chan_obj

            # 获取K线数据
            kl_data = chan[0]
            if not kl_data or len(kl_data.lst) == 0:
                logger.error("K线数据为空，程序返回")
                return None

            # 获取最新K线（CKLine对象包含多个K线单元）
            latest_kline = kl_data.lst[-1]

            # CKLine对象包含多个K线单元，获取最后一个
            if hasattr(latest_kline, 'lst') and latest_kline.lst:
                latest_klu = latest_kline.lst[-1]  # 获取最后一个K线单元
                latest_close = latest_klu.close
                latest_time = latest_klu.time
            else:
                return None

            # 确定用于判断的价格
            if current_price is not None:
                # 使用实时价格
                price_for_judgment = current_price
            else:
                # 使用历史K线最后价格
                price_for_judgment = latest_close

            # 判断中枢是否已确定
            zs_status = "已确定" if latest_zs.is_sure else "未确定"

            # 检查是否有出中枢的笔
            if hasattr(latest_zs, 'bi_out') and latest_zs.bi_out:
                logger.info(f"  该中枢有出笔，索引为: {latest_zs.bi_out.idx}")
            else:
                logger.info(f"  该中枢尚未有笔离开")

            # 条件a: 最新价格位于中枢上沿之上
            condition_a = price_for_judgment > upper_price

            # 条件b: 最新价格不超过中枢上沿的115%
            upper_limit = upper_price * 1.15
            condition_b = price_for_judgment <= upper_limit

            # 条件c: 使用原始K线数据检查最高价，避免缠论库数据缺失问题
            condition_c = True
            max_high_in_range = 0
            upper_125_limit = upper_price * 1.25


            # 获取原始K线数据来检查最高价
            try:
                formatted_code = f"{stock_code}.SH" if stock_code.startswith('6') else f"{stock_code}.SZ"

                kline_data_raw = xtdata.get_market_data(
                    field_list=['time', 'open', 'high', 'low', 'close', 'volume', 'amount'],
                    stock_list=[formatted_code],
                    period='1d',
                    count=100,
                    dividend_type='front',
                    fill_data=True
                )

                if kline_data_raw and 'time' in kline_data_raw:
                    dates = pd.to_datetime(kline_data_raw.get('time').loc[formatted_code], unit='ms', utc=True).dt.tz_convert('Asia/Shanghai').dt.tz_localize(None)
                    df_raw = pd.DataFrame({'日期': dates})

                    for field in ['open', 'high', 'low', 'close', 'volume', 'amount']:
                        if field in kline_data_raw:
                            field_data = kline_data_raw[field]
                            if isinstance(field_data, pd.DataFrame) and formatted_code in field_data.index:
                                df_raw[field] = field_data.loc[formatted_code].values

                    # 找到最近一个收盘价小于中枢上沿的位置
                    for i in range(len(df_raw) - 1, -1, -1):
                        row = df_raw.iloc[i]
                        if row['close'] < upper_price:
                            # 从这个位置开始检查最高价
                            for j in range(i, len(df_raw)):
                                check_row = df_raw.iloc[j]
                                high_price = check_row['high']
                                if high_price > max_high_in_range:
                                    max_high_in_range = high_price
                            break

            except Exception:
                pass

            condition_c = max_high_in_range < upper_125_limit if max_high_in_range > 0 else True

            # 条件d: 中枢结束后的笔数量限制
            condition_d = True
            completed_bi_count = 0

            try:
                # 获取中枢结束位置的索引
                zs_end_idx = latest_zs.end.idx if hasattr(latest_zs, 'end') and hasattr(latest_zs.end, 'idx') else None

                if zs_end_idx is not None and best_chan_obj:
                    kl_data = best_chan_obj[0]

                    # 获取笔列表
                    if hasattr(kl_data, 'bi_list') and kl_data.bi_list:
                        if hasattr(kl_data.bi_list, 'lst'):
                            bi_list = kl_data.bi_list.lst
                        elif hasattr(kl_data.bi_list, '__iter__'):
                            bi_list = list(kl_data.bi_list)
                        else:
                            bi_list = kl_data.bi_list

                        # 首先检查中枢的出笔信息
                        if hasattr(latest_zs, 'bi_out') and latest_zs.bi_out:
                            out_bi = latest_zs.bi_out
                            out_begin_klu = out_bi.get_begin_klu() if hasattr(out_bi, 'get_begin_klu') else None
                            out_end_klu = out_bi.get_end_klu() if hasattr(out_bi, 'get_end_klu') else None

                            if out_begin_klu and hasattr(out_begin_klu, 'idx'):
                                if out_end_klu and hasattr(out_end_klu, 'idx'):
                                    logger.info(f"  条件d检查: 中枢出笔索引 {out_begin_klu.idx}-{out_end_klu.idx} (完整笔)")
                                else:
                                    logger.info(f"  条件d检查: 中枢出笔索引 {out_begin_klu.idx}-未完成")

                        # 统计中枢结束后的完整笔数量
                        first_bi_after_zs = None
                        for i, bi in enumerate(bi_list):
                            try:
                                # 获取笔的开始K线单元
                                begin_klu = bi.get_begin_klu() if hasattr(bi, 'get_begin_klu') else None
                                end_klu = bi.get_end_klu() if hasattr(bi, 'get_end_klu') else None

                                if begin_klu and hasattr(begin_klu, 'idx'):
                                    bi_start_idx = begin_klu.idx
                                    bi_end_idx = end_klu.idx if end_klu and hasattr(end_klu, 'idx') else None

                                    # 修改判断逻辑：检查笔是否在中枢结束后开始或跨越中枢结束点
                                    if bi_start_idx > zs_end_idx or (bi_end_idx and bi_start_idx <= zs_end_idx < bi_end_idx):
                                        # 检查笔是否已经完全确定（有明确的结束点）
                                        if end_klu and hasattr(end_klu, 'idx'):
                                            # 这是一个完整的笔
                                            if first_bi_after_zs is None:
                                                first_bi_after_zs = bi
                                                # 检查第一个笔是否是向上的出笔
                                                if hasattr(begin_klu, 'close') and hasattr(end_klu, 'close'):
                                                    if end_klu.close <= begin_klu.close:
                                                        # 第一个笔不是向上的，不符合条件
                                                        logger.info(f"  条件d检查: 第一个笔不是向上出笔 (起始价{begin_klu.close:.2f} -> 结束价{end_klu.close:.2f})")
                                                        condition_d = False
                                                        break
                                                    else:
                                                        logger.info(f"  条件d检查: 第一个笔是向上出笔 (起始价{begin_klu.close:.2f} -> 结束价{end_klu.close:.2f})")

                                            completed_bi_count += 1

                                            # 如果超过2个完整笔，则不符合条件
                                            if completed_bi_count > 2:
                                                condition_d = False
                                                break
                            except Exception as e:
                                continue

                        logger.info(f"  条件d检查: 中枢结束后共有{completed_bi_count}个完整笔")

                    else:
                        condition_d = False
                else:
                    condition_d = False

            except Exception as e:
                condition_d = False

            # 输出您要求的7类关键信息
            logger.info(f"最终确定中枢的上沿值: {upper_price:.2f}")
            logger.info(f"最终确定中枢的起止时间: {volume_info['start_time']} 到 {volume_info['end_time']}")

            # 输出最佳配置信息
            if best_config:
                # 如果best_config是字符串（配置名称），直接显示
                if isinstance(best_config, str):
                    logger.info(f"使用的最佳配置: {best_config}")
                # 如果best_config是字典，显示详细信息
                elif isinstance(best_config, dict) and best_config:
                    config_str = f"bi_algo={best_config.get('bi_algo', 'N/A')}, seg_algo={best_config.get('seg_algo', 'N/A')}, zs_algo={best_config.get('zs_algo', 'N/A')}"
                    logger.info(f"使用的最佳配置: {config_str}")
                elif isinstance(best_config, dict) and not best_config:
                    logger.info("使用的最佳配置: 配置字典为空")
                else:
                    logger.info(f"使用的最佳配置: {best_config}")
            else:
                logger.info("使用的最佳配置: 未获取到配置信息")

            # 判断中枢是否完结
            if hasattr(latest_zs, 'bi_out') and latest_zs.bi_out:
                bi_out_start_time = None
                bi_out_end_time = None
                # 获取出笔的起始和终止时间
                if hasattr(latest_zs.bi_out, 'begin') and hasattr(latest_zs.bi_out.begin, 'time'):
                    bi_out_start_time = latest_zs.bi_out.begin.time
                if hasattr(latest_zs.bi_out, 'end') and hasattr(latest_zs.bi_out.end, 'time'):
                    bi_out_end_time = latest_zs.bi_out.end.time
                logger.info(f"最终确定中枢是否完结: {zs_status}，出笔的起始终止时间: {bi_out_start_time} 到 {bi_out_end_time}")
            else:
                logger.info(f"最终确定中枢是否完结: {zs_status}，尚未有笔离开")



            # 成交量分析（仅用于信息展示，不影响3买判定）
            logger.info(f"成交量分析:")
            try:
                # 获取中枢时间信息
                zs_start_time = volume_info.get('start_time')
                zs_end_time = volume_info.get('end_time')

                if zs_start_time and zs_end_time:
                    # 进行成交量分析
                    volume_analysis = analyze_volume_in_zs_range(df_renamed, zs_start_time, zs_end_time, logger)

                    if volume_analysis:
                        # a. 中枢内日均成交量
                        logger.info(f"  中枢内日均成交量: {volume_analysis['zs_avg_volume']:.0f}股")

                        # b. 中枢后日成交量峰值
                        if volume_analysis['after_zs_max_volume'] > 0:
                            logger.info(f"  中枢后日成交量峰值: {volume_analysis['after_zs_max_volume']:.0f}股 (日期: {volume_analysis['after_zs_max_date']})")

                            # c. 比较倍数
                            logger.info(f"  中枢后峰值/中枢内均值 = {volume_analysis['volume_ratio']:.2f}倍")
                        else:
                            logger.info(f"  中枢后日成交量峰值: 暂无数据")

                        # d. 中枢内最大周成交量
                        if volume_analysis['zs_weekly_volume'] > 0:
                            logger.info(f"  中枢内最大周成交量: {volume_analysis['zs_weekly_volume']:.0f}股 (周期: {volume_analysis['zs_weekly_period']})")
                        else:
                            logger.info(f"  中枢内最大周成交量: 暂无完整周数据")

                        # e. 中枢后最大周成交量
                        if volume_analysis['after_zs_weekly_volume'] > 0:
                            logger.info(f"  中枢后最大周成交量: {volume_analysis['after_zs_weekly_volume']:.0f}股 (周期: {volume_analysis['after_zs_weekly_period']})")

                            # f. 周成交量对比
                            if volume_analysis['zs_weekly_volume'] > 0:
                                logger.info(f"  中枢后周峰值/中枢内周峰值 = {volume_analysis['weekly_volume_ratio']:.2f}倍")
                        else:
                            logger.info(f"  中枢后最大周成交量: 暂无数据")
                    else:
                        logger.info(f"  成交量分析失败，数据不足")
                else:
                    logger.info(f"  成交量分析跳过，中枢时间信息不完整")
            except Exception as e:
                logger.info(f"  成交量分析出错: {e}")

            # 判断三买过程
            logger.info(f"判断三买过程:")
            logger.info(f"  当前价: {price_for_judgment:.2f}")
            logger.info(f"  中枢上沿: {upper_price:.2f}")
            logger.info(f"  条件a - 收盘价{price_for_judgment:.2f} > 上沿{upper_price:.2f}: {'通过' if condition_a else '不通过'}")
            logger.info(f"  条件b - 收盘价{price_for_judgment:.2f} <= 上沿115%({upper_limit:.2f}): {'通过' if condition_b else '不通过'}")
            logger.info(f"  条件c - 区间最高价{max_high_in_range:.2f} < 上沿125%({upper_125_limit:.2f}): {'通过' if condition_c else '不通过'}")
            logger.info(f"  条件d - 中枢结束后完整笔数量({completed_bi_count}) <= 2: {'通过' if condition_d else '不通过'}")

            # 计算盈亏比
            profit_loss_ratio, ratio_details = calculate_profit_loss_ratio(
                latest_zs, price_for_judgment, best_chan_obj, logger, max_high_in_range
            )

            # 输出盈亏比计算结果
            logger.info(f"盈亏比计算:")
            if ratio_details:
                logger.info(f"  最终盈亏比: {profit_loss_ratio:.4f}")
                logger.info(f"  计算情况: {ratio_details.get('calculation_case', 'N/A')}")
            else:
                logger.info(f"  无法计算盈亏比: {profit_loss_ratio}")

            # 计算中枢能量密度
            try:
                # 获取中枢的上沿和下沿
                zg = upper_price  # 中枢上沿
                zd = latest_zs.low  # 中枢下沿

                # 计算中枢内K线数量
                zs_start_idx = latest_zs.begin.idx if hasattr(latest_zs, 'begin') and hasattr(latest_zs.begin, 'idx') else None
                zs_end_idx = latest_zs.end.idx if hasattr(latest_zs, 'end') and hasattr(latest_zs.end, 'idx') else None

                if zs_start_idx is not None and zs_end_idx is not None and zg > 0 and zd > 0:
                    # 中枢内K线数量
                    zs_kline_count = zs_end_idx - zs_start_idx + 1

                    # 中枢中间价
                    zs_mid_price = (zd + zg) / 2

                    # 中枢相对振幅
                    zs_relative_amplitude = (zg - zd) / zs_mid_price

                    # 中枢能量密度
                    if zs_relative_amplitude > 0:
                        zs_energy_density = zs_kline_count / zs_relative_amplitude
                        logger.info(f"中枢能量密度: {zs_energy_density:.2f} (K线数: {zs_kline_count}, 相对振幅: {zs_relative_amplitude*100:.2f}%)")
                    else:
                        logger.info(f"中枢能量密度: 无法计算 (相对振幅为0)")
                else:
                    logger.info(f"中枢能量密度: 无法计算 (数据不足)")
            except Exception as e:
                logger.info(f"中枢能量密度: 计算出错 ({e})")

            # 判断是否符合3买（包含条件d）
            is_3buy = condition_a and condition_b and condition_c and condition_d

            # 输出判断结论
            if is_3buy:
                logger.info(f"判断结论: 符合3买条件!")
                result = {
                    'stock_code': stock_code,
                    'stock_name': stock_name,
                    'latest_close': latest_close,  # 历史K线最后价格
                    'current_price': current_price,  # 实时价格
                    'price_for_judgment': price_for_judgment,  # 用于判断的价格
                    'zs_upper': upper_price,
                    'zs_lower': latest_zs.low,
                    'zs_mid': latest_zs.mid,
                    'upper_limit': upper_limit,
                    'upper_125_limit': upper_125_limit,
                    'max_high_in_range': max_high_in_range,

                    'latest_time': latest_time,
                    'k_type': k_type,
                    'kline_count': len(csv_lines),  # 添加K线数量
                    'profit_loss_ratio': profit_loss_ratio,  # 添加盈亏比
                    'ratio_details': ratio_details,  # 添加盈亏比详细信息
                    'completed_bi_count': completed_bi_count  # 添加中枢结束后完整笔数量
                }



                return result
            else:
                logger.info(f"判断结论: 不符合3买条件")



                return None

        finally:
            # 清理临时CSV文件（包括配置测试生成的文件）
            try:
                # 清理原始临时文件
                if 'temp_csv_file' in locals() and temp_csv_file and os.path.exists(temp_csv_file):
                    os.remove(temp_csv_file)

                # 清理配置测试生成的临时文件
                if 'temp_csv_file_from_config' in locals() and temp_csv_file_from_config and os.path.exists(temp_csv_file_from_config):
                    os.remove(temp_csv_file_from_config)

            except Exception:
                pass

    except Exception as e:
        logger.error(f"check_3buy_conditions_debug发生异常: {e}")
        import traceback
        logger.error(f"异常堆栈: {traceback.format_exc()}")
        return None


def main():
    """
    主函数
    """
    # 初始化日志系统
    global logger
    logger = setup_logging()

    logger.info("缠论3买点检测程序")
    logger.info("基于QMT数据源和缠论分析")
    logger.info("=" * 50)

    # 选择运行模式
    print("\n请选择运行模式:")
    print("1. 批量分析模式（从XLS文件读取股票列表）")
    print("2. 调试模式（分析单个股票代码）")

    mode = input("请输入模式编号 (1 或 2): ").strip()

    if mode == "2":
        # 调试模式
        print("\n进入调试模式...")

        # 选择分析级别（只需要选择一次）
        print("\n请选择分析级别:")
        print("1. 仅日线级别")
        print("2. 仅30分钟级别")
        print("3. 全部级别（日线+30分钟）")

        level_choice = input("请输入级别编号 (1, 2 或 3): ").strip()
        print(f"选择的级别: {level_choice}")

        if level_choice == "1":
            k_types = [KL_TYPE.K_DAY]
            logger.info("将仅分析日线级别")
        elif level_choice == "2":
            k_types = [KL_TYPE.K_30M]
            logger.info("将仅分析30分钟级别")
        elif level_choice == "3":
            k_types = [KL_TYPE.K_DAY, KL_TYPE.K_30M]
            logger.info("将分析日线和30分钟级别")
        else:
            logger.error("无效的级别选择，使用默认设置（全部级别）")
            k_types = [KL_TYPE.K_DAY, KL_TYPE.K_30M]
            logger.info("将分析日线和30分钟级别")

        # 连续分析模式
        while True:
            print("\n" + "="*50)
            stock_code = input("请输入6位股票代码（输入'q'或'quit'退出）: ").strip()

            if stock_code.lower() in ['q', 'quit', '']:
                print("退出调试模式...")
                break

            if len(stock_code) != 6 or not stock_code.isdigit():
                print("请输入有效的6位股票代码！")
                continue

            print(f"开始分析股票: {stock_code}")
            print(f"\n开始调试分析股票 {stock_code}...")
            debug_single_stock(stock_code, k_types)
            print("调试分析完成！")

    elif mode == "1":
        # 批量分析模式
        # 获取用户输入的XLS文件路径
        xls_file = input("请输入XLS文件路径: ").strip()
        if not xls_file:
            logger.error("未提供文件路径，程序退出...")
            return

        # 移除引号（如果有）
        xls_file = xls_file.strip('"').strip("'")

        # 规范化路径
        try:
            xls_file = os.path.normpath(xls_file)
            logger.info(f"规范化路径: {xls_file}")
        except Exception as e:
            logger.error(f"路径规范化出错: {e}")

        # 检查文件是否存在
        if not os.path.exists(xls_file):
            logger.error(f"文件不存在: {xls_file}")
            # 尝试列出目录中的文件以帮助调试
            try:
                dir_path = os.path.dirname(xls_file)
                if os.path.exists(dir_path):
                    logger.info(f"目录 {dir_path} 中的文件:")
                    for f in os.listdir(dir_path):
                        if f.endswith(('.xls', '.xlsx')):
                            logger.info(f"  {f}")
            except Exception as e:
                logger.error(f"无法列出目录: {e}")
            return

        # 选择分析级别
        print("\n请选择分析级别:")
        print("1. 仅日线级别")
        print("2. 仅30分钟级别")
        print("3. 全部级别（日线+30分钟，任一满足即可）")
        
        level_choice = input("请输入级别编号 (1, 2 或 3): ").strip()
        
        if level_choice == "1":
            k_types = [KL_TYPE.K_DAY]
            logger.info("\n将仅分析日线级别")
        elif level_choice == "2":
            k_types = [KL_TYPE.K_30M]
            logger.info("\n将仅分析30分钟级别")
        elif level_choice == "3":
            k_types = [KL_TYPE.K_DAY, KL_TYPE.K_30M]
            logger.info("\n将同时分析日线和30分钟级别，只要任一级别满足条件即视为符合3买要求")
        else:
            logger.error("无效的级别选择，使用默认设置（全部级别）")
            k_types = [KL_TYPE.K_DAY, KL_TYPE.K_30M]
            logger.info("\n将同时分析日线和30分钟级别，只要任一级别满足条件即视为符合3买要求")

        # 开始分析
        analyze_stocks_from_xls(xls_file, k_types)

        logger.info("\n分析完成!")
        logger.info(f"详细日志已保存到: {LOG_FILE}")

    else:
        logger.error("无效的模式选择，程序退出...")
        return

    input("按回车键退出...")


def get_zs_indicators(stock_code: str, k_type: KL_TYPE = KL_TYPE.K_DAY) -> Dict[str, Any]:
    """
    获取股票最近一个中枢的各项指标

    参数:
        stock_code: 6位股票代码
        k_type: K线级别，默认为日线

    返回:
        包含中枢指标的字典，如果失败返回空字典

    返回字典包含以下键值：
        - zs_upper: 中枢上沿
        - zs_lower: 中枢下沿
        - zs_mid: 中枢中值
        - zs_start_time: 中枢起始时间
        - zs_end_time: 中枢结束时间
        - zs_config: 中枢配置信息
        - zs_energy_density: 中枢能量密度
        - profit_loss_ratio_algo1: 盈亏比算法1结果
        - profit_loss_ratio_algo2: 盈亏比算法2结果
        - profit_loss_ratio_final: 综合盈亏比（取最大值）
        - zs_avg_volume: 中枢内日平均成交量
        - zs_weekly_volume: 中枢内最大周成交量
        - after_zs_max_volume: 中枢后日成交量峰值
        - after_zs_weekly_volume: 中枢后周成交量峰值
        - zs_kline_count: 中枢内K线数量
        - zs_relative_amplitude: 中枢相对振幅
        - current_price: 当前价格
        - error: 错误信息（如果有）
    """
    try:
        # 初始化QMT连接
        if not connect_miniqmt():
            return {"error": "QMT连接失败"}

        # 获取K线数据
        period_map = {
            KL_TYPE.K_DAY: '1d',
            KL_TYPE.K_WEEK: '1w',
            KL_TYPE.K_60M: '60m',
            KL_TYPE.K_30M: '30m',
            KL_TYPE.K_15M: '15m',
            KL_TYPE.K_5M: '5m',
            KL_TYPE.K_1M: '1m'
        }

        period = period_map.get(k_type, '1d')

        # 特殊处理30分钟K线
        if k_type == KL_TYPE.K_30M:
            df_5m = get_miniqmt_data(stock_code, period='5m', count=3000)
            if df_5m.empty or 'error' in df_5m.columns:
                df = get_miniqmt_data(stock_code, period=period, count=1000)
            else:
                df = convert_5min_to_30min(df_5m)
        else:
            df = get_miniqmt_data(stock_code, period=period, count=1000)

        if df.empty or 'error' in df.columns:
            return {"error": "获取K线数据失败"}

        # 统一列名映射
        column_mapping = {
            '日期': 'time', '时间': 'time', 'date': 'time',
            '开盘': 'open', '最高': 'high', '最低': 'low', '收盘': 'close',
            '成交量': 'volume', '成交额': 'amount'
        }
        df_renamed = df.rename(columns=column_mapping)

        # 转换数据格式为缠论所需的CSV格式
        csv_lines = []
        seen_timestamps = set()

        for _, row in df_renamed.iterrows():
            time_str = str(row['time'])

            # 处理时间格式
            if k_type == KL_TYPE.K_30M:
                if '-' in time_str and ' ' in time_str:
                    datetime_part = time_str.split('.')[0]
                    try:
                        dt = datetime.strptime(datetime_part, '%Y-%m-%d %H:%M:%S')
                        date_part = dt.strftime('%Y/%m/%d %H:%M:%S')
                    except:
                        date_part = datetime_part.replace('-', '/').replace('T', ' ')
                elif '-' in time_str:
                    date_part = time_str.split(' ')[0].replace('-', '/') + ' 09:30:00'
                else:
                    date_part = f"{time_str[:4]}/{time_str[4:6]}/{time_str[6:8]} 09:30:00"
            else:
                if '-' in time_str:
                    date_part = time_str.split(' ')[0].replace('-', '/')
                else:
                    date_part = f"{time_str[:4]}/{time_str[4:6]}/{time_str[6:8]}"

            # 检测重复时间戳
            if date_part in seen_timestamps:
                continue
            seen_timestamps.add(date_part)

            # 获取成交量和成交额数据
            volume = row.get('volume', 0) or 0
            amount = row.get('amount', 0) or 0
            turnover_rate = row.get('turnover_rate', 0) or 0

            line = f"{date_part},{row['open']},{row['high']},{row['low']},{row['close']},{volume},{amount},{turnover_rate}"
            csv_lines.append(line)

        # 使用缠论分析获取中枢信息
        upper_price, latest_zs, zs_info, best_config, volume_info, temp_csv_file, best_chan_obj = get_latest_zs_upper_price_with_configs(
            stock_code, csv_lines, k_type
        )

        if upper_price is None or latest_zs is None:
            return {"error": "未找到有效中枢"}

        # 获取当前价格
        current_price = get_current_price(stock_code)
        if current_price is None:
            # 使用最新收盘价作为当前价格
            current_price = float(df_renamed.iloc[-1]['close'])

        # 计算盈亏比
        profit_loss_ratio, ratio_details = calculate_profit_loss_ratio(
            latest_zs, current_price, best_chan_obj, logging.getLogger()
        )

        # 计算中枢能量密度
        zs_energy_density = None
        zs_kline_count = None
        zs_relative_amplitude = None

        try:
            zg = upper_price  # 中枢上沿
            zd = latest_zs.low  # 中枢下沿

            zs_start_idx = latest_zs.begin.idx if hasattr(latest_zs, 'begin') and hasattr(latest_zs.begin, 'idx') else None
            zs_end_idx = latest_zs.end.idx if hasattr(latest_zs, 'end') and hasattr(latest_zs.end, 'idx') else None

            if zs_start_idx is not None and zs_end_idx is not None and zg > 0 and zd > 0:
                zs_kline_count = zs_end_idx - zs_start_idx + 1
                zs_mid_price = (zd + zg) / 2
                zs_relative_amplitude = (zg - zd) / zs_mid_price

                if zs_relative_amplitude > 0:
                    zs_energy_density = zs_kline_count / zs_relative_amplitude
        except:
            pass

        # 分析成交量
        volume_analysis = {}
        if volume_info and volume_info.get('start_time') and volume_info.get('end_time'):
            volume_analysis = analyze_volume_in_zs_range(
                df_renamed, volume_info['start_time'], volume_info['end_time'], logging.getLogger()
            )

        # 构建返回结果
        result = {
            # 中枢基本信息
            "zs_upper": upper_price,
            "zs_lower": latest_zs.low,
            "zs_mid": latest_zs.mid,
            "zs_start_time": volume_info.get('start_time') if volume_info else None,
            "zs_end_time": volume_info.get('end_time') if volume_info else None,

            # 中枢配置
            "zs_config": best_config or "默认配置",

            # 中枢能量密度相关
            "zs_energy_density": zs_energy_density,
            "zs_kline_count": zs_kline_count,
            "zs_relative_amplitude": zs_relative_amplitude,

            # 盈亏比
            "profit_loss_ratio_final": profit_loss_ratio if isinstance(profit_loss_ratio, (int, float)) else None,
            "profit_loss_ratio_algo1": ratio_details.get('algo1_result') if ratio_details else None,
            "profit_loss_ratio_algo2": ratio_details.get('algo2_result') if ratio_details else None,

            # 成交量指标
            "zs_avg_volume": volume_analysis.get('zs_avg_volume'),
            "zs_weekly_volume": volume_analysis.get('zs_weekly_volume'),
            "after_zs_max_volume": volume_analysis.get('after_zs_max_volume'),
            "after_zs_weekly_volume": volume_analysis.get('after_zs_weekly_volume'),

            # 其他信息
            "current_price": current_price,
            "k_type": str(k_type).split('.')[-1] if hasattr(k_type, '__str__') else str(k_type)
        }

        # 清理临时文件
        try:
            if temp_csv_file and os.path.exists(temp_csv_file):
                os.remove(temp_csv_file)
        except:
            pass

        return result

    except Exception as e:
        return {"error": f"计算指标时发生错误: {str(e)}"}


if __name__ == "__main__":
    # 设置控制台编码
    try:
        locale.setlocale(locale.LC_ALL, 'Chinese')
    except:
        pass

    main()

